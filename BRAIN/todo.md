# START 🏁

- [Hoy va a ser un buen día](TODO/buendia.md)


*******************************************************************************

# DOING 📆

- [x] Soporte SaaS
- [x] Responder mail a contadora
- [x] Ver presupuesto Chile

- [ ] Hacer pedido a China Feibot
- [ ] Configurar Tría Cross
- [ ] Agregar códigos de descuentos a Smart Hyrox

---

- [ ] Comprar BTC en OKX
- [ ] Tengo los logos de deportes

- [ ] Luz izq trasera


*******************************************************************************

# FLOW

**API ARCA**
- [ ] ISSUE https://gitlab.com/saasargentina/app/-/issues/2088

**Growth Crono**
- [ ] Actualizar novedades y recuperar newsletter
- [ ] Generar contenido de novedades y para que tenga Prisci, ya está en [CONTENIDO](./CONTENIDO.md)


# QUICK

@mobile
- [ ] Comprar sillas más chicas para crono
- [ ] Configurar Streamings con Meli+

@offline
- [ ] Colgar macetas en el patio
- [ ] Amurar escritorio Mati y sillón


@crono
- [ ] No me llega el botón de pago para el evento de Moxi 2718
- [ ] Cerrar eventos viejos y pasar deudores a Juli
- [ ] Enganchado
- [ ] Probar y ordenar cables


*******************************************************************************

# LATER
