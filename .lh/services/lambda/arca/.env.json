{"sourceFile": "services/lambda/arca/.env", "activeCommit": 0, "commits": [{"activePatchIndex": 23, "patches": [{"date": 1742907291876, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1742919344218, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,28 @@\n BD_HOST=127.0.0.1\n BD_USER=yosoyroot\n BD_PASS=8des4rollo\n BD_BD=saasargentina\n+\n+\n+# Local/Dev environment\n+BD_HOST=localhost\n+BD_USER=root\n+BD_PASS=root\n+BD_BD=saasargentina\n+BD_PORT=3306\n+\n+# Production RDS\n+PROD_BD_HOST=aurora-prod.cluster-xxxxx.region.rds.amazonaws.com\n+PROD_BD_USER=prod_user\n+PROD_BD_PASS=prod_pass\n+PROD_BD_BD=saasargentina\n+PROD_BD_PORT=3306\n+\n+# Beta/Alfa RDS\n+BETA_BD_HOST=aurora-beta.cluster-xxxxx.region.rds.amazonaws.com\n+BETA_BD_USER=beta_user\n+BETA_BD_PASS=beta_pass\n+BETA_BD_BD=saasargentina\n+BETA_BD_PORT=3306\n+\n+APP_ENV=local  # Puede ser: local, production\n\\ No newline at end of file\n"}, {"date": 1748508697177, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,9 +6,9 @@\n \n # Local/Dev environment\n BD_HOST=localhost\n BD_USER=root\n-BD_PASS=root\n+BD_PASS=8des4rollo\n BD_BD=saasargentina\n BD_PORT=3306\n \n # Production RDS\n"}, {"date": 1748508803327, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,9 @@\n \n \n # Local/Dev environment\n BD_HOST=localhost\n-BD_USER=root\n+BD_USER=yosoyroot\n BD_PASS=8des4rollo\n BD_BD=saasargentina\n BD_PORT=3306\n \n"}, {"date": 1748518263657, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,5 +24,5 @@\n BETA_BD_PASS=beta_pass\n BETA_BD_BD=saasargentina\n BETA_BD_PORT=3306\n \n-APP_ENV=local  # Puede ser: local, production\n\\ No newline at end of file\n+APP_ENV=dev  # Puede ser: dev, alfa, beta, prod\n\\ No newline at end of file\n"}, {"date": 1748518269674, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,5 +24,6 @@\n BETA_BD_PASS=beta_pass\n BETA_BD_BD=saasargentina\n BETA_BD_PORT=3306\n \n-APP_ENV=dev  # Puede ser: dev, alfa, beta, prod\n\\ No newline at end of file\n+# Puede ser: dev, alfa, beta, prod\n+APP_ENV=dev  \n\\ No newline at end of file\n"}, {"date": 1748518291015, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,15 +1,11 @@\n+# Puede ser: dev, alfa, beta, prod\n+APP_ENV=dev\n+\n BD_HOST=127.0.0.1\n BD_USER=yosoyroot\n BD_PASS=8des4rollo\n BD_BD=saasargentina\n-\n-\n-# Local/Dev environment\n-BD_HOST=localhost\n-BD_USER=yosoyroot\n-BD_PASS=8des4rollo\n-BD_BD=saasargentina\n BD_PORT=3306\n \n # Production RDS\n PROD_BD_HOST=aurora-prod.cluster-xxxxx.region.rds.amazonaws.com\n@@ -23,7 +19,4 @@\n BETA_BD_USER=beta_user\n BETA_BD_PASS=beta_pass\n BETA_BD_BD=saasargentina\n BETA_BD_PORT=3306\n-\n-# Puede ser: dev, alfa, beta, prod\n-APP_ENV=dev  \n\\ No newline at end of file\n"}, {"date": 1748518299393, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,18 +5,4 @@\n BD_USER=yosoyroot\n BD_PASS=8des4rollo\n BD_BD=saasargentina\n BD_PORT=3306\n-\n-# Production RDS\n-PROD_BD_HOST=aurora-prod.cluster-xxxxx.region.rds.amazonaws.com\n-PROD_BD_USER=prod_user\n-PROD_BD_PASS=prod_pass\n-PROD_BD_BD=saasargentina\n-PROD_BD_PORT=3306\n-\n-# Beta/Alfa RDS\n-BETA_BD_HOST=aurora-beta.cluster-xxxxx.region.rds.amazonaws.com\n-BETA_BD_USER=beta_user\n-BETA_BD_PASS=beta_pass\n-BETA_BD_BD=saasargentina\n-BETA_BD_PORT=3306\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,44 @@\n # Puede ser: dev, alfa, beta, prod\n APP_ENV=dev\n \n+# Base de datos\n BD_HOST=127.0.0.1\n BD_USER=yosoyroot\n BD_PASS=8des4rollo\n BD_BD=saasargentina\n BD_PORT=3306\n+\n+# AWS General\n+AWS_REGION=sa-east-1\n+AWS_ACCOUNT_ID=************\n+\n+# AWS SQS - Colas para AFIP SDK\n+AWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n+AWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\n+AWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\n+AWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\n+\n+# AWS SQS - Cola para emails\n+AWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\n+\n+# AWS SQS - Credenciales para colas\n+AWS_SQS_QUEUER_KEY=********************\n+AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n+\n+# AWS S3 - Buckets\n+AWS_S3_WSFE_BUCKET_DEV=saasargentina-wsfe-dev\n+AWS_S3_WSFE_BUCKET_ALFA=saasargentina-wsfe-alfa\n+AWS_S3_WSFE_BUCKET_BETA=saasargentina-wsfe-beta\n+AWS_S3_WSFE_BUCKET_PROD=saasargentina-wsfe\n+\n+# AWS S3 - Credenciales\n+AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n+AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n+\n+# AFIP - Configuración de ambiente\n+# true para producción, false para homologación\n+AFIP_PRODUCTION=false\n+\n+# Email - Configuración de errores\n+MAIL_SERVIDOR=<EMAIL>\n+MAIL_DESARROLLO=<EMAIL>\n"}, {"date": 1750456970021, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,14 +24,8 @@\n # AWS SQS - Credenciales para colas\n AWS_SQS_QUEUER_KEY=********************\n AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n \n-# AWS S3 - Buckets\n-AWS_S3_WSFE_BUCKET_DEV=saasargentina-wsfe-dev\n-AWS_S3_WSFE_BUCKET_ALFA=saasargentina-wsfe-alfa\n-AWS_S3_WSFE_BUCKET_BETA=saasargentina-wsfe-beta\n-AWS_S3_WSFE_BUCKET_PROD=saasargentina-wsfe\n-\n # AWS S3 - Credenciales\n AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n \n"}, {"date": 1750457293696, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,38 +1,55 @@\n-# Puede ser: dev, alfa, beta, prod\n+# =================================================================\n+# CONFIGURACIÓN DE VARIABLES DE ENTORNO PARA AFIP SDK LAMBDA\n+# =================================================================\n+#\n+# IMPORTANTE: En producción (AWS Lambda), las variables críticas\n+# DEBEN configurarse en AWS Lambda Environment Variables.\n+# Este archivo .env es principalmente para desarrollo local.\n+#\n+# =================================================================\n+\n+# ---------------------------\n+# VARIABLES CRÍTICAS\n+# (DEBEN configurarse en AWS Lambda Environment Variables para prod/beta)\n+# ---------------------------\n+\n+# Ambiente de aplicación\n APP_ENV=dev\n \n-# Base de datos\n+# Base de datos - CRÍTICAS\n BD_HOST=127.0.0.1\n BD_USER=yosoyroot\n BD_PASS=8des4rollo\n BD_BD=saasargentina\n BD_PORT=3306\n \n+# AWS S3 - Credenciales CRÍTICAS\n+AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n+AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n+\n+# AWS SQS - Credenciales CRÍTICAS\n+AWS_SQS_QUEUER_KEY=********************\n+AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n+\n+# AFIP - Configuración CRÍTICA\n+AFIP_PRODUCTION=false\n+\n+# ---------------------------\n+# VARIABLES OPCIONALES\n+# (Pueden quedarse en .env como fallback)\n+# ---------------------------\n+\n # AWS General\n AWS_REGION=sa-east-1\n AWS_ACCOUNT_ID=************\n \n-# AWS SQS - Colas para AFIP SDK\n+# AWS SQS - URLs (pueden usar fallback desde .env)\n AWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n AWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\n AWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\n AWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\n-\n-# AWS SQS - Cola para emails\n AWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\n \n-# AWS SQS - Credenciales para colas\n-AWS_SQS_QUEUER_KEY=********************\n-AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n-\n-# AWS S3 - Credenciales\n-AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n-AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n-\n-# AFIP - Configuración de ambiente\n-# true para producción, false para homologación\n-AFIP_PRODUCTION=false\n-\n-# Email - Configuración de errores\n+# Email - Configuración (pueden usar fallback desde .env)\n MAIL_SERVIDOR=<EMAIL>\n MAIL_DESARROLLO=<EMAIL>\n"}, {"date": 1750460644910, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,10 @@\n # =================================================================\n # CONFIGURACIÓN DE VARIABLES DE ENTORNO PARA AFIP SDK LAMBDA\n # =================================================================\n #\n-# IMPORTANTE: En producción (AWS Lambda), las variables críticas\n-# DEBEN configurarse en AWS Lambda Environment Variables.\n+# IMPORTANTE: En AWS Lambda, el stage se pasa automáticamente desde\n+# serverless.yml como APP_ENV (dev/alfa/beta/prod).\n # Este archivo .env es principalmente para desarrollo local.\n #\n # =================================================================\n \n@@ -12,9 +12,9 @@\n # VARIABLES CRÍTICAS\n # (DEBEN configurarse en AWS Lambda Environment Variables para prod/beta)\n # ---------------------------\n \n-# Ambiente de aplicación\n+# Ambiente de aplicación (en AWS se setea automáticamente desde serverless.yml)\n APP_ENV=dev\n \n # Base de datos - CRÍTICAS\n BD_HOST=127.0.0.1\n"}, {"date": 1750462343426, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -49,7 +49,10 @@\n AWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\n AWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\n AWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\n \n+# AWS S3 - Buckets WSFE (bucket único para todos los ambientes)\n+AWS_S3_WSFE_BUCKET=saasargentina-wsfe\n+\n # Email - Configuración (pueden usar fallback desde .env)\n MAIL_SERVIDOR=<EMAIL>\n MAIL_DESARROLLO=<EMAIL>\n"}, {"date": 1750463254133, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,10 +23,10 @@\n BD_BD=saasargentina\n BD_PORT=3306\n \n # AWS S3 - Credenciales CRÍTICAS\n-AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n-AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n+AWS_S3_KEY=********************\n+AWS_S3_SECRET=ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n \n # AWS SQS - Credenciales CRÍTICAS\n AWS_SQS_QUEUER_KEY=********************\n AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -42,15 +42,8 @@\n # AWS General\n AWS_REGION=sa-east-1\n AWS_ACCOUNT_ID=************\n \n-# AWS SQS - URLs (pueden usar fallback desde .env)\n-AWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n-AWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\n-AWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\n-AWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\n-AWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\n-\n # AWS S3 - Buckets WSFE (bucket único para todos los ambientes)\n AWS_S3_WSFE_BUCKET=saasargentina-wsfe\n \n # Email - Configuración (pueden usar fallback desde .env)\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,4 +48,10 @@\n \n # Email - Configuración (pueden usar fallback desde .env)\n MAIL_SERVIDOR=<EMAIL>\n MAIL_DESARROLLO=<EMAIL>\n+\n+# Gestión de empresas pausadas\n+# Vacío = sin pausas (comportamiento por defecto)\n+# \"*\" = pausar todas las empresas\n+# \"161,162,163\" = pausar empresas específicas\n+PAUSED_COMPANIES=\n\\ No newline at end of file\n"}, {"date": 1750702254673, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,5 +53,5 @@\n # Gestión de empresas pausadas\n # Vacío = sin pausas (comportamiento por defecto)\n # \"*\" = pausar todas las empresas\n # \"161,162,163\" = pausar empresas específicas\n-PAUSED_COMPANIES=\n\\ No newline at end of file\n+IDEMPRESAS_PAUSADAS=\n\\ No newline at end of file\n"}, {"date": 1750702265535, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,5 +53,5 @@\n # Gestión de empresas pausadas\n # Vacío = sin pausas (comportamiento por defecto)\n # \"*\" = pausar todas las empresas\n # \"161,162,163\" = pausar empresas específicas\n-IDEMPRESAS_PAUSADAS=\n\\ No newline at end of file\n+IDEMPRESAS_PAUSADAS=*\n\\ No newline at end of file\n"}, {"date": 1750702279583, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,5 +53,5 @@\n # Gestión de empresas pausadas\n # Vacío = sin pausas (comportamiento por defecto)\n # \"*\" = pausar todas las empresas\n # \"161,162,163\" = pausar empresas específicas\n-IDEMPRESAS_PAUSADAS=*\n\\ No newline at end of file\n+IDEMPRESAS_PAUSADAS=\n\\ No newline at end of file\n"}, {"date": 1750702290314, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,5 +53,5 @@\n # Gestión de empresas pausadas\n # Vacío = sin pausas (comportamiento por defecto)\n # \"*\" = pausar todas las empresas\n # \"161,162,163\" = pausar empresas específicas\n-IDEMPRESAS_PAUSADAS=\n\\ No newline at end of file\n+IDEMPRESAS_PAUSADAS=160\n\\ No newline at end of file\n"}, {"date": 1750702298468, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,5 +53,5 @@\n # Gestión de empresas pausadas\n # Vacío = sin pausas (comportamiento por defecto)\n # \"*\" = pausar todas las empresas\n # \"161,162,163\" = pausar empresas específicas\n-IDEMPRESAS_PAUSADAS=160\n\\ No newline at end of file\n+IDEMPRESAS_PAUSADAS=161,162,163\n\\ No newline at end of file\n"}, {"date": 1750702305154, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,5 +53,5 @@\n # Gestión de empresas pausadas\n # Vacío = sin pausas (comportamiento por defecto)\n # \"*\" = pausar todas las empresas\n # \"161,162,163\" = pausar empresas específicas\n-IDEMPRESAS_PAUSADAS=161,162,163\n\\ No newline at end of file\n+IDEMPRESAS_PAUSADAS=\n\\ No newline at end of file\n"}, {"date": 1750717804886, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -31,9 +31,9 @@\n AWS_SQS_QUEUER_KEY=********************\n AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n \n # AFIP - Configuración CRÍTICA\n-AFIP_PRODUCTION=false\n+AFIP_PRODUCTION=true\n \n # ---------------------------\n # VARIABLES OPCIONALES\n # (Pueden quedarse en .env como fallback)\n"}, {"date": 1750718046178, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -31,8 +31,9 @@\n AWS_SQS_QUEUER_KEY=********************\n AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n \n # AFIP - Configuración CRÍTICA\n+AFIPSDK_ACCESS_TOKEN=w2Cw7sKWKAX7g8DfSGoM5tBSTrysSrNPTPEQSgJb9heSrfkMH8OLjA6ly8zBHuqb\n AFIP_PRODUCTION=true\n \n # ---------------------------\n # VARIABLES OPCIONALES\n"}], "date": 1742907291876, "name": "Commit-0", "content": "BD_HOST=127.0.0.1\nBD_USER=yosoyroot\nBD_PASS=8des4rollo\nBD_BD=saasargentina\n"}]}