

<!-- PROJECT SHIELDS -->
[![Packagist][packagist-shield]](https://packagist.org/packages/afipsdk/afip.php)
[![Contributors][contributors-shield]](https://github.com/afipsdk/afip.php/graphs/contributors)
[![Closed issues][issues-shield]](https://github.com/afipsdk/afip.php/issues)


<!-- PROJECT LOGO -->
<br />
<p align="center">
  <a href="https://github.com/afipsdk/afip.php">
    <img src="https://github.com/afipsdk/afipsdk.github.io/blob/master/images/logo-colored.png" alt="Logo" width="130" height="130">
  </a>

  <h3 align="center">Afip.php</h3>

  <p align="center">
    Librería para conectarse a los Web Services de AFIP
    <br />
    <a href="https://docs.afipsdk.com"><strong>Explorar documentación »</strong></a>
    <br />
    <br />
    <a href="https://discord.gg/A6TuHEyAZm"><strong>Comunidad Afip SDK</strong></a>
    <br />
    <br />
    <a href="https://github.com/afipsdk/afip.php/issues">Reportar un bug</a>
  </p>
</p>


<!-- DOCS -->
## Documentación
[Explorar documentación](https://docs.afipsdk.com)

<!-- COMUNITY -->
## Comunidad
[Comunidad Afip SDK](https://discord.gg/A6TuHEyAZm)


<!-- ABOUT THE PROJECT -->
## Acerca del proyecto
Con más de 100k descargas, desde el 2017, Afip SDK es la plataforma preferida entre los desarrolladores para conectarse a los web services de ARCA.

<!-- CONTACT -->
### Contacto
Soporte de Afip SDK - <EMAIL>

Link del proyecto: [https://github.com/afipsdk/afip.php](https://github.com/afipsdk/afip.php)


_Este software y sus desarrolladores no tienen ninguna relación con la AFIP._

<!-- MARKDOWN LINKS & IMAGES -->
[packagist-shield]: https://img.shields.io/packagist/dt/afipsdk/afip.php.svg?logo=php&?logoColor=white
[contributors-shield]: https://img.shields.io/github/contributors/afipsdk/afip.php.svg?color=orange
[issues-shield]: https://img.shields.io/github/issues-closed-raw/afipsdk/afip.php.svg?color=blueviolet
[license-shield]: https://img.shields.io/github/license/afipsdk/afip.php.svg?color=blue

