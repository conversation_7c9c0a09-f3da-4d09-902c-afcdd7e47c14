<?php
function recuperar_sesion()
{
    session_name("saasargentina");
    session_set_cookie_params(0, '/', URL_COOKIE);
    session_start();
}

function hash_session_token()
{
    return hash('sha256', $_SESSION['empresa_idempresa'].$_SESSION['usuario_idusuario'].session_id());
}

function mostrar_error($valor, $continuar = false)
{
    global $modulo;
    global $a;
    global $id;
    global $boton;
    global $ventana;
    global $id_boton;

    // Si estamos ejecutando por consola
    if (!isset($_SESSION['empresa_idempresa']) && in_array($modulo, ['script']))
        exit('ERROR: '.$valor);

    // Si estamos ejecutando por crontab
    if (!isset($_SESSION['empresa_idempresa']) && in_array($modulo, ['crontab'])) {
        global $idempresa;
        global $script;
        $modulo = $script['idscript'];

    } else {
        $idempresa = $_SESSION['empresa_idempresa'];
    }

    $texto = 'ERROR: '.$valor.'<br /><br />
        empresa_idempresa: <a href="'.URL_SAAS.'/saas.php?a=verempresa&id='.$idempresa.'">'.$idempresa.'</a><br />
        empresa_nombre: '.$_SESSION['empresa_nombre'].'<br />
        empresa_mail: '.$_SESSION['empresa_mail'].'<br />
        usuario_idusuario: <a href="'.URL_SAAS.'/saas.php?a=altaticket&idusuario='.$_SESSION['usuario_idusuario'].'">'.$_SESSION['usuario_idusuario'].'</a><br />
        usuario_mail: '.$_SESSION['usuario_mail'].'<br />
        usuario_nombre: '.$_SESSION['usuario_nombre'].'<br />
        mail: '.$_SESSION['mail'].' ('.$_POST['mail'].')<br />
        <br />
        modulo: '.$modulo.'<br />
        a: '.$a.'<br />
        id: '.$id.'<br />
        boton: '.$boton.'<br />
        ventana: '.$ventana.'<br />
        id_boton: '.$id_boton.'<br />
        continuar: '.$continuar.'<br /><br />
        $_SESSION[a]: '.$_SESSION['a'].'<br /><br />
        $_REQUEST: '.json_encode($_REQUEST).'<br />';

    if (ESTADO != 'desarrollo') {
        enviar_mail(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR', $texto);
        if (!$continuar) {
            exit('<script>document.location = "'.URL_ERROR.'";</script>');
        }

    } else {
        exit("<br /><br />\r\n\r\n".$texto);
    }
}

function enviar_mail($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $cuenta = 'default', $adjuntos = array())
{
    global $modulo;

    if (ESTADO == 'desarrollo')
        return true;

    if (!$asunto)
        return false;

    // Revisar si $asunto tiene las palabras con las que se mandó SPAM
    if (preg_match('/bloqueo|urgente|sospecha/i', $asunto)) {
        mostrar_error('No se envió un mail porque el asunto contiene palabras prohibidas: '.$asunto, true);
        return false;
    }

    if ($modulo != 'crontab' && $modulo != 'script'
        && isset($_SESSION['empresa_idempresa']) && $_SESSION['empresa_idempresa']) {
        // Empresas que bloqueamos el envío de mails porque hicieron SPAM
        if ($_SESSION['empresa_idempresa'] && in_array($_SESSION['empresa_idempresa'], [8902, 2630]))
            return false;

        // Revisar si en la variable de sesion 'hora_ultimo_mail' se envió un mail hace menos de x segundos
        if ($_SESSION['hora_ultimo_mail'] && (time() - $_SESSION['hora_ultimo_mail']) < SEGUNDOS_ENTRE_MAILS) {
            mostrar_error("No se envió un mail porque fue hace menos de ".SEGUNDOS_ENTRE_MAILS." segundos. El asunto es: ".$asunto, true);
            return false;
        }
        $_SESSION['hora_ultimo_mail'] = time();
    }


    // Si es sqs lo mando a la cola
    if (($cuenta === 'sqs' || $cuenta === 'default')
        && function_exists('email_queue'))
        return email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente, $adjuntos);

    if ($cuenta === 'default') // No existe funcion email_queue
        $cuenta = 'no-reply';


    // Voy con algún SMTP
    require_once PATH_TOOLS.'swiftmailer/lib/swift_required.php';

    try {

        switch ($cuenta) {
            case 'info':
                $cuenta = array(
                    'servidor' => 'smtp.gmail.com',
                    'puerto' => 465,
                    'seguridad' => 'ssl',
                    'autenticacion' => 0,
                    'user' => SMTP_INFO_USER,
                    'pass' => SMTP_INFO_PASS,
                    );
                $texto.= '<br><br><hr>Enviado desde <a href="'.URL_SITE.'">'.URL_HOST.'</a>';
                break;

            case 'no-reply':
                $cuenta = array(
                    'servidor' => 'smtp.gmail.com',
                    'puerto' => 465,
                    'seguridad' => 'ssl',
                    'autenticacion' => 0,
                    'user' => SMTP_NOREPLY_USER,
                    'pass' => SMTP_NOREPLY_PASS,
                    );
                $texto.= '<br><br><hr>Enviado desde <a href="'.URL_SITE.'">'.URL_HOST.'</a>';
                break;

            case 'ses':
                $cuenta = array(
                    'servidor' => 'email-smtp.sa-east-1.amazonaws.com',
                    'puerto' => 587,
                    'seguridad' => 'tls',
                    'autenticacion' => 0,
                    'user' => SMTP_SES_USER,
                    'pass' => SMTP_SES_PASS,
                    );
                $copia_remitente = false;
                if (is_array($remitente)
                    && count($remitente) > 1
                    && $remitente[0] == MAIL_SERVIDOR) {
                    $responder = $remitente[1];
                    $remitente = MAIL_SERVIDOR;
                } else {
                    $responder = $remitente;
                }
                $texto.= '<br><br><hr>Enviado desde <a href="'.URL_SITE.'">'.URL_HOST.'</a>';
                break;

            default:
                if (!(is_array($cuenta)
                    && isset($cuenta['servidor'])
                    && isset($cuenta['puerto'])
                    && isset($cuenta['seguridad'])
                    && isset($cuenta['autenticacion'])
                    && isset($cuenta['user'])
                    && isset($cuenta['pass'])
                    ))
                    mostrar_error('Función enviar_mail con parámetro cuenta incorrecto: '.json_encode($cuenta), true);
                break;
        }

        // Si tengo más de un destinatario, el segundo va como copia y el resto lo borro
        // porque no queremos que se use como sistema de newsletter (tampoco mantengo el nombre)
        if (is_array($destinatario)
            && count($destinatario) > 1
            && $destinatario[0] != MAIL_INFO) {
            $copia = $destinatario[1];
            $destinatario = array($destinatario[0]);
        } else {
            $copia = false;
        }

        if (!isset($responder))
            $responder = $remitente;

        $transporte = Swift_SmtpTransport::newInstance($cuenta['servidor'], $cuenta['puerto'], $cuenta['seguridad'])
            -> setUsername($cuenta['user'])
            -> setPassword($cuenta['pass']);

        $envio = Swift_Message::newInstance()
            -> setFrom($remitente)
            -> setSender($remitente)
            -> setReplyTo($responder)
            -> setTo($destinatario)
            -> setSubject($asunto)
            -> setContentType('text/html')
            -> setBody($texto, 'text/html');
        if ($copia)
            $envio -> setCc($copia);
        if ($copia_remitente)
            $envio -> setBcc($responder);

        foreach ($adjuntos as $adjunto) {
            switch ($adjunto['tipo']) {
                default:
                case 'archivo':
                    $envio -> attach(Swift_Attachment::fromPath($adjunto['nombre']));
                    break;

                case 'html':
                    $envio -> attach(Swift_Attachment::newInstance()
                        -> setFilename($adjunto['nombre'])
                        -> setContentType('text/html')
                        -> setBody($adjunto['valor'])
                        );
                    break;

                case 'pdf':
                    $envio -> attach(Swift_Attachment::newInstance()
                        -> setFilename($adjunto['nombre'])
                        -> setContentType('application/pdf')
                        -> setBody($adjunto['valor'])
                        );
                    break;

                case 'planilla':
                    $envio -> attach(Swift_Attachment::newInstance()
                        -> setFilename($adjunto['nombre'])
                        -> setContentType('application/vnd.ms-excel')
                        -> setBody($adjunto['valor'])
                        );
                    break;
            }
        }

        return Swift_Mailer::newInstance($transporte) -> send($envio);

    } catch (Exception $e) {
        $separador = ';';
        file_put_contents(PATH_LOGS . 'mail_caido.csv',
            date("Y-m-d H:i:s") . $separador
            . 'ERROR' . $separador
            . $_SESSION['empresa_idempresa'] . $separador
            . $cuenta['user'] . $separador
            . str_replace($separador, ',', $e->getMessage()) . "\r\n"
            , FILE_APPEND);
    }
}

function generar_random($tamaño = 50) {
    $chars = "abcdefghijkmnopqrstuvwxyz023456789ABCDEFGHIJKMNOPQRSTUVWXYZ";
    $random = '';
    for($i = 0; $i < $tamaño; $i++)
        $random.= $chars[rand(0, 58)];

    return $random;
}

function redondeo($numero)
{
    return (isset($_SESSION['control_formato_separador_miles']) && $_SESSION['control_formato_separador_miles'])
        ? number_format(round($numero, 2), 2, ',', '.')
        : number_format(round($numero, 2), 2, '.', '');
}

function responder_json($estado = 'ok', $datos = array()) {
    $respuesta = array(
        'estado' => $estado
        );
    foreach ($datos as $key => $value) {
        $respuesta[$key] = $value;
    }

    header('Content-type: application/json');
    if ($estado == 'error')
        http_response_code(400);

    $callback = filter_input(INPUT_GET, 'callback');
    echo ($callback ? $callback . '(' . json_encode($respuesta) . ')' : json_encode($respuesta));
    exit();
}

function listar_meses($desde, $hasta = false)
{
    if (!$hasta)
        $hasta = date("Y-m-d");
    $meses = array();
    $desde_destripado = explode('-', substr($desde, 0, 7)); // Separo solo la fecha por si acaso trae la hora
    $hasta_destripado = explode('-', substr($hasta, 0, 7));

    $desde_año = (int)$desde_destripado[0];
    $desde_mes = (int)$desde_destripado[1];
    $hasta_año = (int)$hasta_destripado[0];
    $hasta_mes = (int)$hasta_destripado[1];

    while ($desde_año <= $hasta_año) {
        while (($desde_año < $hasta_año && $desde_mes < 13)
            || ($desde_año == $hasta_año && $desde_mes <= $hasta_mes && $desde_mes < 13)) {
            $meses[] = completar_numero($desde_mes, 2) . '-' . completar_numero($desde_año, 4);
            $desde_mes++;
        }
        $desde_mes = 1;
        $desde_año++;
    }

    return $meses;
}

function vacios($cantidad) {
    $temp = '';
    for ($i = 0; $i < $cantidad; $i++) {
        $temp.= ' ';
    }
    return $temp;
}

function ceros($cant)
{
    $return = '';
    for ($i = 0; $i < $cant; $i++)
        $return.= '0';
    return $return;
}

function completar_texto($valor, $digitos)
{
    $return = '';
    $largo_valor = mb_strlen($valor, 'UTF-8');
    if ($largo_valor > $digitos) {
        $return = mb_substr($valor, 0, $digitos);
    } else {
        $return = $valor;
        for ($i = 0; $i < ($digitos - $largo_valor); $i++) {
            $return.= ' ';
        }
    }
    return $return;
}

function completar_numero($valor, $digitos)
{
    if ($valor == 0) {
        $return = '';
        for($j = 0; $j < $digitos; $j++)
            $return.='0';

        return $return;
    }

    $negativo = $valor < 0;

    if ($negativo) {
        $valor = $valor * -1;
        $digitos--;
    }

    $valor = "$valor";
    $return = '';
    $largo_valor = strlen($valor);
    if ($largo_valor > $digitos) {
        $return = substr($valor, 0, $digitos);
    } else {
        $return = $valor;
        for ($i = 0; $i < ($digitos - $largo_valor); $i++) {
            $return = '0'.$return;
        }
    }
    return ($negativo ? '-' : '') . $return;
}

function numero_comprobante($letra, $puntodeventa, $numero)
{
    return $letra.completar_numero($puntodeventa, 5).'-'.completar_numero($numero, 8);
}

function url_amigable($valor) {
    $separador = '-';
    $valor = strtolower($valor);
    $valor = str_replace(
        array('á', 'é', 'í', 'ó', 'ú', 'ñ', '&', '+', '-', '_', "\r", "\r\n", "\n"),
        array('a', 'e', 'i', 'o', 'u', 'n', 'y', 'y', ' ', ' ', ' ', ' ', ' '),
        $valor);
    $valor = trim(preg_replace("/[^ a-z0-9]/", "", $valor));
    $valor = str_replace(' ', $separador, $valor);

    return $valor;
}


function sanear_string($string)
{

    $string = trim($string);

    $string = str_replace(
        array('á', 'à', 'ä', 'â', 'ª', 'Á', 'À', 'Â', 'Ä'),
        array('a', 'a', 'a', 'a', 'a', 'A', 'A', 'A', 'A'),
        $string
    );

    $string = str_replace(
        array('é', 'è', 'ë', 'ê', 'É', 'È', 'Ê', 'Ë'),
        array('e', 'e', 'e', 'e', 'E', 'E', 'E', 'E'),
        $string
    );

    $string = str_replace(
        array('í', 'ì', 'ï', 'î', 'Í', 'Ì', 'Ï', 'Î'),
        array('i', 'i', 'i', 'i', 'I', 'I', 'I', 'I'),
        $string
    );

    $string = str_replace(
        array('ó', 'ò', 'ö', 'ô', 'Ó', 'Ò', 'Ö', 'Ô'),
        array('o', 'o', 'o', 'o', 'O', 'O', 'O', 'O'),
        $string
    );

    $string = str_replace(
        array('ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'),
        array('u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'),
        $string
    );

    $string = str_replace(
        array('ñ', 'Ñ', 'ç', 'Ç'),
        array('n', 'N', 'c', 'C',),
        $string
    );

    //Esta parte se encarga de eliminar cualquier caracter extraño
    $string = str_replace(
        array("¨", "º", "~",
             "#", "@", "|", "!",
             "·", "$", "%", "&", "/",
             "?", "'", "¡",
             "¿", "[", "^", "<code>", "]",
             "+", "}", "{", "¨", "´",
             ">", "< ", ";", ",", ":",
             "."),
        '',
        $string
    );

    return $string;
}

function calcular_cuit($dni, $codigo_sexo) {

    $dni = completar_numero($dni, 8);
    $multiplicadores = Array('3', '2', '7','6', '5', '4', '3', '2');
    $calculo = (substr($codigo_sexo, 0, 1) * 5) + (substr($codigo_sexo, 1, 1) * 4);

    for ( $i=0 ; $i<8 ; $i++ ) {
        $calculo += substr($dni,$i,1) * $multiplicadores[$i];
    }

    $resto = ($calculo) % 11;

    if (($codigo_sexo != '30') && ($resto <= 1)) {
        if ($resto == 0) {
            $ultimo_digito_cuit = '0';
        } else {
            if ($codigo_sexo == 20) {
                $ultimo_digito_cuit = '9';
            } else {
                $ultimo_digito_cuit = '4';
            }
        }
        $codigo_sexo = '23';
    } else {
        $ultimo_digito_cuit = 11 - $resto;
    }

    return $codigo_sexo . $dni . $ultimo_digito_cuit;
}

function escape_no_sql($unescaped) {
    $replacements = array(
        "\x00"=>'\x00',
        "\n"=>'\n',
        "\r"=>'\r',
        "\\"=>'\\\\',
        "'"=>"\'",
        '"'=>'\"',
        "\x1a"=>'\x1a',
    );
    return strtr($unescaped, $replacements);
}

function queue_ml($url, $body)
{
    file_put_contents(PATH_LOGS.'notificaciones/'.date("Y-m-d_H-i").'.log',
        date("Y-m-d_H-i-s")
        . '|||'
        . $url
        . '|||'
        . $body
        . "\r\n"
        , FILE_APPEND);
}

function en_rfce($cuit)
{
    $cuit = (string)$cuit;
    if (strlen($cuit) != 11)
        return false;

    $rfce_listado = file_get_contents(__DIR__.'/rfce_listado.txt');
    return strpos($rfce_listado, $cuit) === false
        ? false
        : true;
}

function mes($idmes)
{
    $mes = array(
        1 => 'Enero',
        2 => 'Febrero',
        3 => 'Marzo',
        4 => 'Abril',
        5 => 'Mayo',
        6 => 'Junio',
        7 => 'Julio',
        8 => 'Agosto',
        9 => 'Septiembre',
        10 => 'Octubre',
        11 => 'Noviembre',
        12 => 'Diciembre'
        );
    return $mes[$idmes];
}

function es_franquiciado($idempresa, $franquicia = false)
{
    switch ($franquicia) {
        default: // Todos los franquiciados
            if (in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12348, 12355]))
                return 874;
            else
                return false;
            break;

        case 'gaming': // Franquicia de Gaming City
        case 874:
            return in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12348, 12355]);
            break;
    }
}

function sin_control_numeracion($idempresa)
{
    // Empresas anteriores que ya arreglaron la numeración
    // 8142, 7398, 2443, 301, 798, 5914, 8828, 10008
    // Empresas excluidas del control de numeración porque usaron otro sistema
    return in_array($idempresa, [8905, 8980, 11597, 11854]);
}

function sin_fullsearch($idempresa)
{
    $empresas_sin_fullsearch = [];
    return in_array($idempresa, $empresas_sin_fullsearch);
}

function tipoDocs($id = false, $sin_cuit = false) {
    $tipoDocs = [
        ['id' => 99, 'valor' => 'Ninguno'],
        ['id' => 80, 'valor' => 'CUIT'],
        ['id' => 96, 'valor' => 'DNI'],
        ['id' => 87, 'valor' => 'CDI'],
        ['id' => 91, 'valor' => 'CI Extranjera'],
        ['id' => 94, 'valor' => 'Pasaporte'],
    ];

    if ($id) {
        foreach ($tipoDocs as $tipoDoc) {
            if ($tipoDoc['id'] == $id) {
                return $tipoDoc['valor'];
            }
        }
    }

    if ($sin_cuit) {
        unset($tipoDocs[1]);
    }

    return $tipoDocs;
}

function como_fe($idempresa) {
    // Por ahora las opciones son: pyafipws, pyafipws_2025, afipsdk, afipsdk_lambda
    if (in_array($idempresa, [161]))
        return 'afipsdk';

    return 'pyafipws';
}
