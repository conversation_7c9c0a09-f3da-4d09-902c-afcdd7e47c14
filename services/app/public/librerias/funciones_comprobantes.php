<?php
if ($GLOBALS['modulo'] == 'ventas') {
    $GLOBALS['modulo'] = 'ventas';
    $GLOBALS['modulo_singular'] = 'venta';
    $GLOBALS['idmodulo'] = 'idventa';
    $GLOBALS['idx'] = 'idproductoxventa';
    $GLOBALS['idtercero'] = 'idcliente';
    $GLOBALS['tercero'] = 'clientes';
} elseif ($GLOBALS['modulo'] == 'compras') {
    $GLOBALS['modulo'] = 'compras';
    $GLOBALS['modulo_singular'] = 'compra';
    $GLOBALS['idmodulo'] = 'idcompra';
    $GLOBALS['idx'] = 'idproductoxcompra';
    $GLOBALS['idtercero'] = 'idproveedor';
    $GLOBALS['tercero'] = 'proveedores';
} elseif ($GLOBALS['modulo'] == 'productos') {
    $GLOBALS['modulo'] = 'productos';
    $GLOBALS['modulo_singular'] = 'producto';
    $GLOBALS['idmodulo'] = 'idproducto';
    if (strpos($a, 'traslado') !== false || strpos($ventana, 'traslado') !== false) {
        $GLOBALS['idx'] = 'idproductoxtraslado';
    } else {
        $GLOBALS['idx'] = 'idproductoxcombo';
    }
}


// Devuelve matrices de ciertos tipos de comprobantes
function comprobantes_array($tipo) {
    switch ($tipo) {
        // Tipos de compras de los comprobantes que utilizan el formato de letra pto de venta y nro
        case 'tipos_compras_fiscales':
            $resultado_sql = consulta_sql(
                "SELECT idtipocompra
                    FROM categorias_compras
                WHERE esfiscal = '1'");
            $return = array();
            while ($temp_array = array_sql($resultado_sql)) {
                $return[] = $temp_array['idtipocompra'];
            }
            return $return;
            break;

        case 'tipos_compras_fiscales_sql':
            return campo_sql(consulta_sql(
                "SELECT GROUP_CONCAT(DISTINCT idtipocompra SEPARATOR ', ')
                    FROM categorias_compras
                WHERE esfiscal = '1'"), 0);
            break;
    }
}

function comprobantes_discrimina($id)
{
    $discrimina = campo_sql(consulta_sql($GLOBALS['modulo'] == 'ventas'
        ? "SELECT discrimina FROM categorias_ventas WHERE idtipoventa =
            (SELECT idtipoventa FROM ventas WHERE idventa = '$id' LIMIT 1) LIMIT 1"
        : "SELECT discrimina FROM compras WHERE idcompra = '$id' LIMIT 1"
        ), 0);

    return $discrimina;
}

function comprobantes_cargar($id)
{
    $sql = $GLOBALS['modulo'] == 'ventas'
        ? "SELECT subtotal, descuento, neto, nogravado, exento, iva, tributos, total,
                categorias_ventas.discrimina, ventas.idtipoventa, idcliente,
                tiporelacion, idrelacion, ventas.operacioninversa, ventas.estado,
                monedas.simbolo
            FROM ventas
                LEFT JOIN categorias_ventas ON categorias_ventas.idtipoventa = ventas.idtipoventa
                LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
            WHERE idventa = '$id' LIMIT 1"
        : "SELECT subtotal, descuento, neto, nogravado, exento, iva, tributos, total,
                discrimina, idtipocompra, idproveedor, tiporelacion, idrelacion, operacioninversa,
                monedas.simbolo
            FROM compras
                LEFT JOIN monedas ON compras.idmoneda = monedas.idmoneda
            WHERE idcompra = '$id' LIMIT 1";

    return array_sql(consulta_sql($sql));
}

function comprobantes_guardar($id, $comprobante, $admin = 'link')
{
    $update = array('subtotal', 'neto', 'nogravado', 'exento', 'iva', 'tributos', 'total');
    foreach ($update as $key => $value) {
        $update[$key] = $GLOBALS['modulo'] . "." . $value . " = '" . $comprobante[$value] . "'";
    }

    $sql = $GLOBALS['modulo'] == 'ventas'
        ? "UPDATE ventas SET " . implode(', ', $update) . " WHERE idventa = '$id' LIMIT 1"
        : "UPDATE compras SET " . implode(', ', $update) . " WHERE idcompra = '$id' LIMIT 1";
    consulta_sql($sql, $admin);

    if ($GLOBALS['modulo'] == 'ventas')
        $buscar_monedas = [
            'clientes' => $comprobante['idcliente'],
            'ventas' => $id,
        ];
    else
        $buscar_monedas = [
            'proveedores' => $comprobante['idproveedor'],
            'compras' => $id,
        ];
    $idmonedas = idmonedas($buscar_monedas);

    $total = ($comprobante['operacioninversa'] ? -1 * $comprobante['total'] : $comprobante['total']);

    $sql = $GLOBALS['modulo'] == 'ventas'
        ? "UPDATE ventasxclientes SET total = "
            .cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $total)."
            WHERE idtipoventa = '".$comprobante['idtipoventa']."' AND id = '$id' LIMIT 1"
        : "UPDATE comprasxproveedores SET total = "
            .cotizacion($idmonedas['proveedores'], $idmonedas['compras'], $total)."
            WHERE idtipocompra = '".$comprobante['idtipocompra']."' AND id = '$id' LIMIT 1";
    consulta_sql($sql, $admin);

    //cargar el ivasxcompras/ivasxventas
    $tabla = $GLOBALS['modulo'] == 'ventas'? 'ivasxventas' : 'ivasxcompras';
    $tabla_id = $GLOBALS['modulo'] == 'ventas'? 'idventa' : 'idcompra';

    //borramos lo que existia
    $sql = "DELETE FROM $tabla WHERE $tabla_id = '$id'";
    consulta_sql($sql, $admin);

    foreach ($comprobante['ivas'] as $key => $iva) {
        if (($iva['idiva'] == 3 && $comprobante['ivas'][$key]['base'] > 0) // Iva 0% tiene total $0 pero con base mayor tiene que aparecer
            || ($iva['idiva'] > 3 && $comprobante['ivas'][$key]['total'] > 0)) {
            $sql = 'INSERT INTO '.$tabla.' SET
                '.$tabla_id.' ='.$id.',
                idiva = '.$iva['idiva'].',
                iva = '.$comprobante['ivas'][$key]['total'];
            consulta_sql($sql, $admin);
        }
    }
    return $comprobante;
}

function comprobantes_ivas()
{
    $ivas = array();

    $resultado_sql = consulta_sql("SELECT campo, nombre, valor FROM tablas_ivas");
    while ($iva = array_sql($resultado_sql)) {
        $ivas[$iva['campo']] = $iva['valor'];
    }

    return $ivas;
}

function comprobantes_importe($productoxcomprobante, $discrimina)
{
    switch ($discrimina) {
        case 'A':
        case 'C':
            $importe = redondeo(
                $productoxcomprobante['cantidad']
                * $productoxcomprobante['precio']
                * (1 - $productoxcomprobante['descuento'] / 100)
                );
            break;

        case 'B':
            $importe = redondeo(
                $productoxcomprobante['cantidad']
                * $productoxcomprobante['preciofinal']
                * (1 - $productoxcomprobante['descuento'] / 100)
                );
            break;

        case 'R':
        case 'traslado':
            $importe = 0;
            break;

        case 'combo':
            $importe = redondeo(
                $productoxcomprobante['cantidad']
                * ($_SESSION['configuracion_discrimina'] ? $productoxcomprobante['preciofinal'] : $productoxcomprobante['precio'])
                * (1 - $productoxcomprobante['descuento'] / 100)
                );
            break;
    }

    return $importe;
}

function comprobantes_costo_combo($productoxcomprobante, $discrimina)
{
    $costocombo = redondeo(
    $productoxcomprobante['cantidad']
    * $productoxcomprobante['costo']
    );

    return $costocombo;
}

function comprobantes_precio_combo($productoxcomprobante, $discrimina)
{
    $preciocombo = redondeo(
    $productoxcomprobante['cantidad']
    * $productoxcomprobante['precio']
    );

    return $preciocombo;
}

function comprobantes_bases_imponibles($id, $discrimina, $admin = 'link')
{
    $bases_imponibles = array();

    $sql = $GLOBALS['modulo'] == 'ventas'
        ? "SELECT cantidad, costo, precio, preciofinal, descuento,
                tablas_ivas.idiva, tablas_ivas.campo, tablas_ivas.valor
            FROM productosxventas
                LEFT JOIN tablas_ivas ON productosxventas.idiva = tablas_ivas.idiva
            WHERE idventa = '$id'"
        : "SELECT cantidad, costo, descuento,
                tablas_ivas.idiva, tablas_ivas.campo, tablas_ivas.valor
            FROM productosxcompras
                LEFT JOIN tablas_ivas ON productosxcompras.idiva = tablas_ivas.idiva
            WHERE idcompra = '$id'";

    // Recorro los productos para generar las bases imponibles de cada IVA
    $resultado_sql = consulta_sql($sql, $admin);
    while ($productoxcomprobante = array_sql($resultado_sql)) {
        if($productoxcomprobante['campo'] != 'nogravado' && $productoxcomprobante['campo'] != 'exento') {
           $productoxcomprobante['valor'] = ($productoxcomprobante['valor'] + 0) . ' %';
           $productoxcomprobante[7] = ($productoxcomprobante['valor'] + 0) . ' %';
        }
        switch ($discrimina) {
            case 'A':
            case 'B':
                if ($GLOBALS['modulo'] == 'compras')
                    $productoxcomprobante['precio'] = $productoxcomprobante['costo'];

                // Redondeo a cada importe
                $productoxcomprobante['importe'] = comprobantes_importe($productoxcomprobante, $discrimina);

                $bases_imponibles[$productoxcomprobante['campo']] ['total']+= $productoxcomprobante['importe'];
                $bases_imponibles[$productoxcomprobante['campo']] ['iva'] = $productoxcomprobante['valor'];
                $bases_imponibles[$productoxcomprobante['campo']] ['idiva'] = $productoxcomprobante['idiva'];
                break;

            case 'C':
                if ($GLOBALS['modulo'] == 'compras')
                    $productoxcomprobante['precio'] = $productoxcomprobante['costo'];
                $productoxcomprobante['importe'] = comprobantes_importe($productoxcomprobante, $discrimina);
                if ($GLOBALS['modulo'] == 'ventas')
                    $bases_imponibles['neto']+= $productoxcomprobante['importe'];
                else
                    $bases_imponibles['nogravado']+= $productoxcomprobante['importe'];
                break;

            case 'R':
            case 'combo':
            default:
                break;
        }
    }

    return $bases_imponibles;
}

function comprobantes_recalculando($id, $admin = 'link')
{
    if (!$id)
        mostrar_error("Se llamó a comprobantes_recalculando sin id '$id'");

    $temp_array = consulta_sql("SELECT SUM(importe) AS totalimporte FROM tributosx".$GLOBALS['modulo']." WHERE " . $GLOBALS['idmodulo'] . " ='".$id."'", $admin);
    $totaltributos = array_sql($temp_array);
    if (!$totaltributos['totalimporte'] )
        $totaltributos['totalimporte'] = 0;
    consulta_sql("UPDATE  " . $GLOBALS['modulo'] . " SET tributos = '".$totaltributos['totalimporte']."' WHERE " . $GLOBALS['idmodulo'] . " = '".$id."' LIMIT 1", $admin);

    $comprobante = comprobantes_cargar($id);
    $comprobante['tributos'] = $totaltributos['totalimporte'];
    $comprobante['subtotal'] = 0;
    $comprobante['neto'] = 0;
    $comprobante['nogravado'] = 0;
    $comprobante['exento'] = 0;
    $comprobante['ivas'] = array();
    $comprobante['impuestos'] = 0;
    $comprobante['total_con_iva'] = 0;
    $comprobante['iva'] = 0;
    $comprobante['total_bases_iva'] = 0;
    $comprobante['totalnetos'] = 0;
    $comprobante['total'] = 0;
    $comprobante['totalsindescuento'] = 0;
    $comprobante['descuentoenpesos'] = 0;
    $comprobante['totalivas'] = 0;
    $comprobante['totaltributos'] = 0;

    // Re-calculo los totales
    switch ($comprobante['discrimina']) {
        case 'A':
            $ivas = comprobantes_ivas();
            $bases_imponibles = comprobantes_bases_imponibles($id, $comprobante['discrimina'], $admin);

            foreach ($bases_imponibles as $key => $value) {
                $comprobante['ivas'][$key]['idiva'] = $value['idiva'];                                                  //idiva
                $comprobante['ivas'][$key]['iva'] = $value['iva'];                                                      //% iva

                $comprobante['subtotal'] += redondeo($value['total']);                                                  //subtotal

                if($value['idiva'] != '1' && $value['idiva'] != '2') {
                    $comprobante['totalsindescuento'] += redondeo($value['total'] + ($value['total'] * $value['iva'] / 100));
                } else {
                    $comprobante['totalsindescuento'] += redondeo($value['total']);
                }

                $value['total'] = redondeo($value['total'] * (1 - $comprobante['descuento'] / 100));            // Desc en bases imponibles

                $comprobante['ivas'][$key]['base'] = $value['total'];
                if($value['idiva'] == '1') {
                    $comprobante['nogravado'] = redondeo($value['total']);
                    $comprobante['ivas'][$key]['total'] = redondeo(($value['total'] * $value['iva']) / 100);
                } else if($value['idiva'] == '2') {
                    $comprobante['exento'] = redondeo($value['total']);
                    $comprobante['ivas'][$key]['total'] = redondeo(($value['total'] * $value['iva']) / 100);
                } else {
                    $comprobante['ivas'][$key]['total'] = redondeo(($value['total'] * $value['iva']) / 100);
                    $comprobante['neto'] += $value['total'];
                }

                $comprobante['totalivas'] += redondeo($comprobante['ivas'][$key]['total']);

            }

            $comprobante['subtotal'] = redondeo($comprobante['subtotal']);
            $comprobante['totalnetos'] = redondeo($comprobante['nogravado'] + $comprobante['exento'] + $comprobante['neto']);
            $comprobante['descuentoenpesos'] = redondeo($comprobante['subtotal'] - ($comprobante['subtotal'] * (1 - $comprobante['descuento'] / 100)));
            $comprobante['totalivas'] = redondeo($comprobante['totalivas']);
            $comprobante['totaltributos'] = redondeo($comprobante['tributos']);
            break;

        case 'B':
            // No puede haber compras con discrimina = B
            $ivas = comprobantes_ivas();
            $bases_imponibles = comprobantes_bases_imponibles($id, $comprobante['discrimina'], $admin);

            foreach ($bases_imponibles as $key => $value) {
                $comprobante['ivas'][$key]['idiva'] = $value['idiva'];                                                  //idiva
                $comprobante['ivas'][$key]['iva'] = $value['iva'];                                                      //% iva

                $comprobante['subtotal'] += redondeo($value['total']);                                                  //subtotal
                $comprobante['totalsindescuento'] += $value['total'];                                                   //total sin desc
                $bases_imponibles[$key] = redondeo($value['total'] * (1 - $comprobante['descuento'] / 100));            // nuevas bases imponibles con descuento
                $iva = floatval($value['iva']);                                                                         // % iva para operar
                $comprobante['base_iva'][$key] = redondeo($bases_imponibles[$key] / (1 + $iva / 100));
                $comprobante['ivas'][$key]['base'] = $comprobante['base_iva'][$key];
                $comprobante['ivas'][$key]['total'] = redondeo($bases_imponibles[$key] - $comprobante['base_iva'][$key]);   //El iva que mando para insert
                $comprobante['totalivas'] += $comprobante['ivas'][$key]['total'];                                           // Totalivas

                if($value['idiva'] != '1' && $value['idiva'] != '2') {
                    $comprobante['neto'] += redondeo($comprobante['base_iva'][$key]);                                       // Neto (gravado)
                }

            }
            $comprobante['subtotal'] = redondeo($comprobante['subtotal']);
            $comprobante['nogravado'] = $bases_imponibles['nogravado'];
            $comprobante['exento'] = $bases_imponibles['exento'];
            $comprobante['totalnetos'] = redondeo($comprobante['nogravado'] + $comprobante['exento'] + $comprobante['neto']);
            $comprobante['descuentoenpesos'] = redondeo($comprobante['subtotal'] - ($comprobante['subtotal'] * (1 - $comprobante['descuento'] / 100)));
            $comprobante['totalivas'] = redondeo($comprobante['totalivas']);
            $comprobante['totaltributos'] = redondeo($comprobante['tributos']);
            break;

        case 'C':

            $bases_imponibles = comprobantes_bases_imponibles($id, $comprobante['discrimina'], $admin);

            $comprobante['subtotal'] = $comprobante['totalsindescuento'] = $GLOBALS['modulo'] == 'ventas'
                ? redondeo($bases_imponibles['neto'])
                : redondeo($bases_imponibles['nogravado']);

            // Aplico el descuento a las bases imponibles y las redondeo
            foreach ($bases_imponibles as $key => $value) {
                $bases_imponibles[$key] = redondeo($value * (1 - $comprobante['descuento'] / 100));
            }

            $comprobante['totaltributos'] = redondeo($comprobante['tributos']);
            $comprobante['neto'] = $GLOBALS['modulo'] == 'ventas' ? redondeo($bases_imponibles['neto']) : redondeo($comprobante['nogravado']);
            $comprobante['nogravado'] = redondeo($bases_imponibles['nogravado']);
            $comprobante['descuentoenpesos'] = $GLOBALS['modulo'] == 'ventas'
                ? redondeo($comprobante['subtotal'] - $comprobante['neto'])
                : redondeo($comprobante['subtotal'] - $bases_imponibles['nogravado']);

            break;

        case 'R':
            break;

        case 'combo':
            break;

        default:
            break;
    }

    $comprobante['iva'] = redondeo($comprobante['totalivas']);

    if($GLOBALS['modulo'] == 'compras' && $comprobante['discrimina'] == 'C')
        $comprobante['total']  = redondeo($comprobante['subtotal'] + $comprobante['tributos'] - $comprobante['descuentoenpesos']);
    else
        $comprobante['total'] = redondeo($comprobante['neto'] + $comprobante['nogravado'] + $comprobante['exento'] + $comprobante['totalivas']  + $comprobante['tributos']);

    $comprobante = comprobantes_guardar($id, $comprobante, $admin);

    return $comprobante;
}

function comprobantes_mostrar_totales($comprobante, $discrimina)
{
    global $i18n;
    switch ($discrimina) {
        case 'A':
            $comprobante['totalnetos'] = redondeo($comprobante['neto'] + $comprobante['nogravado'] + $comprobante['exento']);
            $comprobante['totalivas'] = redondeo($comprobante['iva']);
            $bases_imponibles = comprobantes_bases_imponibles(($GLOBALS['modulo'] == 'ventas' ? $comprobante['idventa'] : $comprobante['idcompra']), $discrimina);
            foreach ($bases_imponibles as $key => $value) {
                $iva = floatval($value['iva']);
                $value['total'] = redondeo($value['total'] * (1 - $comprobante['descuento'] / 100));
                $comprobante['totalivas'] += $value['total'] * $value['iva'] / 100;
            }
            $comprobante['totaltributos'] = $comprobante['tributos'];
            $comprobante['descuentoenpesos'] = redondeo($comprobante['subtotal'] - ($comprobante['subtotal'] * (1 - $comprobante['descuento'] / 100)));
            //$comprobante['descuentoenpesos'] = redondeo($comprobante['subtotal'] - $comprobante['totalnetos']);
            texto('moneda', $i18n[68], $comprobante['subtotal'], 'auto', false, false, false, 'id="subtotal"', $comprobante['simbolo']);
            texto('moneda', $i18n[69] . ' (' .$comprobante['descuento'] . ' %)', $comprobante['descuentoenpesos'], 'auto', false, false, false, 'id="descuentoenpesos"', $comprobante['simbolo']);
            texto('moneda', $i18n[70], $comprobante['totalnetos'], 'auto', false, false, false, 'id="totalnetos"', $comprobante['simbolo']);
            texto('moneda', $i18n[6], $comprobante['iva'], 'auto', false, false, false, 'id="totalivas"', $comprobante['simbolo']);
            texto('moneda', $i18n[194], $comprobante['totaltributos'], 'auto', false, false, false, 'id="totaltributos"', $comprobante['simbolo']);
            texto('moneda', $i18n[74], $comprobante['total'], 'auto', false, false, false, 'id="total"', $comprobante['simbolo']);
            break;

        case 'B':
            $comprobante['totalnetos'] = redondeo($comprobante['neto'] + $comprobante['nogravado'] + $comprobante['exento']);
            $bases_imponibles = comprobantes_bases_imponibles(($GLOBALS['modulo'] == 'ventas' ? $comprobante['idventa'] : $comprobante['idcompra']), $discrimina);
            foreach ($bases_imponibles as $key => $value) {
                $iva = floatval($value['iva']);
                $value['total'] = redondeo($value['total'] * (1 - $comprobante['descuento'] / 100));
                $comprobante['totalivas'] += $value['total'] * $value['iva'] / 100;
            }
            $comprobante['descuentoenpesos'] = redondeo($comprobante['subtotal'] - ($comprobante['subtotal'] * (1 - $comprobante['descuento'] / 100)));
            //$comprobante['descuentoenpesos'] = redondeo($comprobante['subtotal'] - $comprobante['totalnetos'] - $comprobante['totalivas']);
            $comprobante['totaltributos'] = $comprobante['tributos'];
            texto('moneda', $i18n[68], $comprobante['subtotal'], 'auto', false, false, false, 'id="subtotal"', $comprobante['simbolo']);
            texto('moneda', $i18n[69] . ' (' .$comprobante['descuento'] . ' %)', $comprobante['descuentoenpesos'], 'auto', false, false, false, 'id="descuentoenpesos"', $comprobante['simbolo']);
            texto('moneda', $i18n[194], $comprobante['totaltributos'], 'auto', false, false, false, 'id="totaltributos"', $comprobante['simbolo']);
            texto('moneda', $i18n[74], $comprobante['total'], 'auto', false, false, false, 'id="total"', $comprobante['simbolo']);
            break;

        case 'C':
            $comprobante['descuentoenpesos'] = $GLOBALS['modulo'] == 'ventas'
                ? redondeo($comprobante['subtotal'] - $comprobante['neto'])
                : redondeo($comprobante['subtotal'] - $comprobante['nogravado']);
            texto('moneda', $i18n[68], $comprobante['subtotal'], 'auto', false, false, false, 'id="subtotal"', $comprobante['simbolo']);
            texto('moneda', $i18n[69] . ' (' .$comprobante['descuento'] . ' %)', $comprobante['descuentoenpesos'], 'auto', false, false, false, 'id="descuentoenpesos"', $comprobante['simbolo']);
            if ($GLOBALS['modulo'] == 'compras') {
                $comprobante['totaltributos'] = $comprobante['tributos'];
                texto('moneda', $i18n[194], $comprobante['totaltributos'], 'auto', false, false, false, 'id="totaltributos"', $comprobante['simbolo']);
            }
            texto('moneda', $i18n[74], $comprobante['total'], 'auto', false, false, false, 'id="total"', $comprobante['simbolo']);
            break;

        case 'combo':

        case 'R':
        default:
            break;
    }
}

function comprobantes_mostrar_titulo_productosxcomprobantes($discrimina)
{
    global $i18n_funciones;

    linea_inicio('titulo', 2);
    {
        switch ($discrimina) {
            case 'A':
                celda('texto', '', 'imagen');
                celda('texto', $i18n_funciones[172], '15'); // Código
                celda('texto', $i18n_funciones[173], '7'); // Cantidad
                celda('texto', $i18n_funciones[174], '38'); // Producto
                celda('texto', $i18n_funciones[175], '10'); // Precio
                celda('texto', $i18n_funciones[178], '7'); // IVA
                celda('texto', $i18n_funciones[179], '7');
                celda('texto', $i18n_funciones[177]);
                break;

            case 'B':
                celda('texto', '', 'imagen');
                celda('texto', $i18n_funciones[172], '15'); // Código
                celda('texto', $i18n_funciones[173], '7'); // Cantidad
                celda('texto', $i18n_funciones[174], '38'); // Producto
                celda('texto', $i18n_funciones[178], '7'); // IVA
                celda('texto', $i18n_funciones[176], '10'); // Precio final
                celda('texto', $i18n_funciones[179], '7');
                celda('texto', $i18n_funciones[177]);
                break;

            case 'C':
                celda('texto', '', 'imagen');
                celda('texto', $i18n_funciones[172], '15'); // Código
                celda('texto', $i18n_funciones[173], '7'); // Cantidad
                celda('texto', $i18n_funciones[174], '48'); // Producto
                celda('texto', $i18n_funciones[175], '10'); // Precio
                celda('texto', $i18n_funciones[179], '7');
                celda('texto', $i18n_funciones[177]);
                break;

            case 'R':
                celda('texto', '', 'imagen');
                celda('texto', $i18n_funciones[172], '15'); // Código
                celda('texto', $i18n_funciones[173], '7'); // Cantidad
                celda('texto', $i18n_funciones[174]); // Producto
                break;

            case 'traslado':
                celda('texto', $i18n_funciones[172], '15'); // Código
                celda('texto', $i18n_funciones[173], '7'); // Cantidad
                celda('texto', $i18n_funciones[174]); // Producto
                break;

            case 'combo':
                celda('texto', $i18n_funciones[172], '10'); // Código
                celda('texto', $i18n_funciones[173], '7'); // Cantidad
                celda('texto', $i18n_funciones[174], '38'); // Producto
                celda('texto', $i18n_funciones[181], '7'); // Stock
                if ($_SESSION['configuracion_discrimina'])
                    celda('texto', $i18n_funciones[178], '7'); // IVA
                celda('texto', $i18n_funciones[182], '7'); // Costo
                celda('texto', $i18n_funciones[177], '8'); // Importe
                break;
        }
    }
    linea_fin();
}

function comprobantes_ver_productoxcomprobante($productoxcomprobante, $discrimina)
{
    global $i18n_funciones;
    global $i18n;

    $productoxcomprobante['importe'] = comprobantes_importe($productoxcomprobante, $discrimina);
    $productoxcomprobante['costocombo'] = comprobantes_costo_combo($productoxcomprobante, $discrimina);

    linea_inicio('fila', 2, false, 'id="linea_'.$productoxcomprobante[$GLOBALS['idx']].'"');
    {
        entrada('hidden', 'idproducto', '', $productoxcomprobante['idproducto']);
        if ($productoxcomprobante['observacion'] && $discrimina != 'combo')
            celda('imagen', $i18n_funciones[20], 'imagen', 'vermas', 'vermas');
        else if ($discrimina != 'combo' && $discrimina != 'traslado')
            celda('imagen', false, 'imagen');

        switch ($discrimina) {
            case 'A':
                celda('largo', $productoxcomprobante['codigo'], '15', false,
                    ($productoxcomprobante['idproducto'] ? 'productos.php?a=ver&id=' . $productoxcomprobante['idproducto'] : false));
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                celda('moneda', $productoxcomprobante['precio'], '10', false, false, false, false, $productoxcomprobante['simbolo']);
                celda('iva', $productoxcomprobante['iva'], '7');
                celda('descuento', ($productoxcomprobante['descuento'] > 0 ? $productoxcomprobante['descuento'] : ''), '7');
                celda('moneda', $productoxcomprobante['importe'], false, false, false, false, false, $productoxcomprobante['simbolo']);
                break;

            case 'B':
                celda('largo', $productoxcomprobante['codigo'], '15', false,
                    ($productoxcomprobante['idproducto'] ? 'productos.php?a=ver&id=' . $productoxcomprobante['idproducto'] : false));
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                celda('iva', $productoxcomprobante['iva'], '7');
                celda('moneda', $productoxcomprobante['preciofinal'], '10', false, false, false, false, $productoxcomprobante['simbolo']);
                celda('descuento', ($productoxcomprobante['descuento'] > 0 ? $productoxcomprobante['descuento'] : ''), '7');
                celda('moneda', $productoxcomprobante['importe'], false, false, false, false, false, $productoxcomprobante['simbolo']);
                break;

            case 'C':
                celda('largo', $productoxcomprobante['codigo'], '15', false,
                    ($productoxcomprobante['idproducto'] ? 'productos.php?a=ver&id=' . $productoxcomprobante['idproducto'] : false));
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre'], '48'); // Producto
                celda('moneda', $productoxcomprobante['precio'], '10', false, false, false, false, $productoxcomprobante['simbolo']);
                celda('descuento', ($productoxcomprobante['descuento'] > 0 ? $productoxcomprobante['descuento'] : ''), '7');
                celda('moneda', $productoxcomprobante['importe'], false, false, false, false, false, $productoxcomprobante['simbolo']);
                break;

            case 'R':
            case 'traslado':
                celda('largo', $productoxcomprobante['codigo'], '15', false,
                    ($productoxcomprobante['idproducto'] ? 'productos.php?a=ver&id=' . $productoxcomprobante['idproducto'] : false));
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre']); // Producto
                break;

            case 'combo':
                celda('largo', $productoxcomprobante['codigo'], '10', false,
                    ($productoxcomprobante['idproducto'] ? 'productos.php?a=ver&id=' . $productoxcomprobante['idproducto'] : false));
                celda('cantidad', $productoxcomprobante['cantidad'], '7', false, false, ' id="cantidad"');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                celda('cantidad', $productoxcomprobante['stockactual'], '7', false, false, ' id="stockactual"');
                if ($_SESSION['configuracion_discrimina'])
                    celda('iva', $productoxcomprobante['iva'], '7');
                celda('moneda', $productoxcomprobante['costocombo'], '7', false, false, ' id="costocombo"', false, $productoxcomprobante['simbolo']);
                celda('moneda', $productoxcomprobante['importe'], '10', '7', false, ' id="importe"', false, $productoxcomprobante['simbolo']);
                break;
        }
        salto_linea();
        bloque_inicio('linea_'.$productoxcomprobante[$GLOBALS['idx']].'_oculta', 'class="oculto" style="display:none;"');
        {
            observacion($i18n_funciones[71], $productoxcomprobante['observacion']);
        }
        bloque_fin();
    }
    if ($_SESSION['modulo_trazabilidad'])
        linea_fin(array(array('tipo' => 'flotante', 'url' => 'productosx'.$GLOBALS['modulo'].'_ver.php?id='.$productoxcomprobante[$GLOBALS['idx']], 'a' => 'detalle', 'title' => $i18n[64], 'permiso' => 'productos_ver')));
    else
        linea_fin();
}

//Agrega líneas
function comprobantes_mod_productoxcomprobante($productoxcomprobante, $discrimina)
{
    global $i18n_funciones;
    global $i18n;

    $productoxcomprobante['importe'] = comprobantes_importe($productoxcomprobante, $discrimina);
    $productoxcomprobante['preciocombo'] = comprobantes_precio_combo($productoxcomprobante, $discrimina);
    $productoxcomprobante['costocombo'] = comprobantes_costo_combo($productoxcomprobante, $discrimina);
    linea_inicio('fila', 2, false, 'id="linea_'.$productoxcomprobante[$GLOBALS['idx']].'"');
    {
        entrada('hidden', 'idx', '', $productoxcomprobante[$GLOBALS['idx']]);
        entrada('hidden', 'idlista', '', $productoxcomprobante['idlista']);
        entrada('hidden', 'iddeposito', '', $productoxcomprobante['iddeposito']);
        entrada('hidden', 'idproducto', '', $productoxcomprobante['idproducto']);
        switch ($discrimina) {
            case 'A':
                celda('imagen', $i18n_funciones[20], 'imagen', 'vermas', 'vermas');
                celda('largo', $productoxcomprobante['codigo'], '15');
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                celda('moneda', $productoxcomprobante['precio'], '10', false, false, false, false, $productoxcomprobante['simbolo']);
                celda('iva', $productoxcomprobante['iva'], '7');
                celda('descuento', ($productoxcomprobante['descuento'] > 0 ? $productoxcomprobante['descuento'] : ''), '7');
                celda('moneda', $productoxcomprobante['importe'], false, false, false, false, false, $productoxcomprobante['simbolo']);
                break;

            case 'B':
                celda('imagen', $i18n_funciones[20], 'imagen', 'vermas', 'vermas');
                celda('largo', $productoxcomprobante['codigo'], '15');
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                celda('iva', $productoxcomprobante['iva'], '7');
                celda('moneda', $productoxcomprobante['preciofinal'], '10', false, false, false, false, $productoxcomprobante['simbolo']);
                celda('descuento', ($productoxcomprobante['descuento'] > 0 ? $productoxcomprobante['descuento'] : ''), '7');
                celda('moneda', $productoxcomprobante['importe'], false, false, false, false, false, $productoxcomprobante['simbolo']);
                break;

            case 'C':
                celda('imagen', $i18n_funciones[20], 'imagen', 'vermas', 'vermas');
                celda('largo', $productoxcomprobante['codigo'], '15');
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre'], '48'); // Producto
                celda('moneda', $productoxcomprobante['precio'], '10', false, false, false, false, $productoxcomprobante['simbolo']);
                celda('descuento', ($productoxcomprobante['descuento'] > 0 ? $productoxcomprobante['descuento'] : ''), '7');
                celda('moneda', $productoxcomprobante['importe'], false, false, false, false, false, $productoxcomprobante['simbolo']);
                break;

            case 'R':
                celda('imagen', $i18n_funciones[20], 'imagen', 'vermas', 'vermas');
                celda('largo', $productoxcomprobante['codigo'], '15');
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre']); // Producto
                break;

            case 'traslado':
                celda('largo', $productoxcomprobante['codigo'], '15');
                celda('cantidad', $productoxcomprobante['cantidad'], '7');
                celda('largo', $productoxcomprobante['nombre']); // Producto
                break;

            case 'combo':
                celda('largo', $productoxcomprobante['codigo'], '10');
                celda('cantidad', $productoxcomprobante['cantidad'], '7', false, false, ' id="cantidad"');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                celda('cantidad', $productoxcomprobante['stockactual'], '7', false, false, ' id="stockactual"');
                if ($_SESSION['configuracion_discrimina'])
                    celda('iva', $productoxcomprobante['iva'], '7');
                celda('moneda', $productoxcomprobante['costocombo'], '7', false, false, ' id="costocombo"', false, $productoxcomprobante['simbolo']);
                entrada('hidden', 'preciocombo', '', $productoxcomprobante['preciocombo']);
                celda('moneda', $productoxcomprobante['importe'], '10', '7', false, ' id="importe"', false, $productoxcomprobante['simbolo']);
                break;
        }
        salto_linea();
        if ($discrimina != 'combo' && $discrimina != 'traslado') {
            bloque_inicio('linea_'.$productoxcomprobante[$GLOBALS['idx']].'_oculta', 'class="oculto" style="display:none;"');
            {
                observacion($i18n_funciones[71], $productoxcomprobante['observacion']);
            }
            bloque_fin();
        }
    }
    $url_mod = $discrimina == 'traslado'
        ? 'productosxtraslados.php?a=mod&id='.$productoxcomprobante[$GLOBALS['idx']]
        : 'productosx'.$GLOBALS['modulo'].'.php?a=mod&id='.$productoxcomprobante[$GLOBALS['idx']];
    $url_baja = $discrimina == 'traslado'
        ? 'productosxtraslados.php?a=baja&id='.$productoxcomprobante[$GLOBALS['idx']]
        : 'productosx'.$GLOBALS['modulo'].'.php?a=baja&id='.$productoxcomprobante[$GLOBALS['idx']];
    linea_fin(array(
        array('tipo' => 'ajax', 'url' => $url_mod, 'a' => 'mod', 'title' => $i18n[100]),
        array('tipo' => 'ajax', 'url' => $url_baja, 'a' => 'baja', 'title' => $i18n[101])));

    linea_inicio('fila', 2, false, 'id="entrada_'.$productoxcomprobante[$GLOBALS['idx']].'" style="display: none;"');
    {
        entrada('hidden', 'idx', '', $productoxcomprobante[$GLOBALS['idx']]);
        entrada('hidden', 'idlista', '', $productoxcomprobante['idlista']);
        entrada('hidden', 'iddeposito', '', $productoxcomprobante['iddeposito']);
        entrada('hidden', 'idproducto', '', $productoxcomprobante['idproducto']);
        entrada('hidden', 'cantidad_original', '', $productoxcomprobante['cantidad']);

        if ($discrimina != 'combo' && $discrimina != 'traslado')
            celda('imagen', $i18n_funciones[20], 'imagen', 'vermas', 'vermas');
        switch ($discrimina) {
            case 'A':
                celda('largo', $productoxcomprobante['codigo'], '15');
                entrada('cantidad', 'cantidad', '', $productoxcomprobante['cantidad'], '7', false, false, 'onkeypress="trigger_ok(event)"');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                if ($GLOBALS['modulo'] == 'ventas' && !$_SESSION['perfil_ventas_mod_precios']) {
                    entrada('hidden', 'precio', false, $productoxcomprobante['precio']);
                    entrada('moneda', 'precio_no', false, $productoxcomprobante['precio'], '10', '11', false, 'disabled="disabled"');
                    celda('iva', $productoxcomprobante['iva'], '7');
                    entrada('descuento', 'descuento', false, $productoxcomprobante['descuento'], '7', '', false, 'disabled="disabled"');
                } else {
                    entrada('moneda', 'precio', '', $productoxcomprobante['precio'], '10', false, false, 'onkeypress="trigger_ok(event)"');
                    celda('iva', $productoxcomprobante['iva'], '7');
                    entrada('descuento', 'descuento', false, $productoxcomprobante['descuento'], '7', false, false, 'onkeypress="trigger_ok(event)"');
                }
                celda('moneda', $productoxcomprobante['importe'], '10', false, false, 'class="importe"', false, $productoxcomprobante['simbolo']);
                break;

            case 'B':
                celda('largo', $productoxcomprobante['codigo'], '15');
                entrada('cantidad', 'cantidad', '', $productoxcomprobante['cantidad'], '7', false, false, 'onkeypress="trigger_ok(event)"');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                celda('iva', $productoxcomprobante['iva'], '7');
                if ($GLOBALS['modulo'] == 'ventas' && !$_SESSION['perfil_ventas_mod_precios']) {
                    entrada('moneda', 'preciofinal_no', false, $productoxcomprobante['preciofinal'], '10', '11', false, 'disabled="disabled"', $productoxcomprobante['simbolo']);
                    entrada('hidden', 'preciofinal', false, $productoxcomprobante['preciofinal']);
                    entrada('descuento', 'descuento', false, $productoxcomprobante['descuento'], '7', '', false, 'disabled="disabled"');
                } else {
                    entrada('moneda', 'preciofinal', '', $productoxcomprobante['preciofinal'], '10', false, false, 'onkeypress="trigger_ok(event)"', $productoxcomprobante['simbolo']);
                    entrada('descuento', 'descuento', false, $productoxcomprobante['descuento'], '7', false, false, 'onkeypress="trigger_ok(event)"');
                }
                celda('moneda', $productoxcomprobante['importe'], '10', false, false, 'class="importe"', false, $productoxcomprobante['simbolo']);

                break;

            case 'C':
                celda('largo', $productoxcomprobante['codigo'], '15');
                entrada('cantidad', 'cantidad', '', $productoxcomprobante['cantidad'], '7', false, false, 'onkeypress="trigger_ok(event)"');
                celda('largo', $productoxcomprobante['nombre'], '48'); // Producto
                if ($GLOBALS['modulo'] == 'ventas' && !$_SESSION['perfil_ventas_mod_precios']) {
                    entrada('moneda', 'precio_no', false, $productoxcomprobante['precio'], '10', '11', false, 'disabled="disabled"', $productoxcomprobante['simbolo']);
                    entrada('hidden', 'precio', false, $productoxcomprobante['precio']);
                    entrada('descuento', 'descuento', false, $productoxcomprobante['descuento'], '7', '', false, 'disabled="disabled"');
                } else {
                    entrada('moneda', 'precio', '', $productoxcomprobante['precio'], '10', false, false, 'onkeypress="trigger_ok(event)"', $productoxcomprobante['simbolo']);
                    entrada('descuento', 'descuento', false, $productoxcomprobante['descuento'], '7', false, false, 'onkeypress="trigger_ok(event)"');
                }
                celda('moneda', $productoxcomprobante['importe'], '10', false, false, 'class="importe"', false, $productoxcomprobante['simbolo']);
                break;

            case 'R':
            case 'traslado':
                celda('largo', $productoxcomprobante['codigo'], '15');
                entrada('cantidad', 'cantidad', '', $productoxcomprobante['cantidad'], '7', false, false, 'onkeypress="trigger_ok(event)"');
                celda('largo', $productoxcomprobante['nombre']); // Producto
                break;

            case 'combo':
                celda('largo', $productoxcomprobante['codigo'], '10');
                entrada('cantidad', 'cantidad', '', $productoxcomprobante['cantidad'], '7', false, false, 'tabindex="4"');
                celda('largo', $productoxcomprobante['nombre'], '38'); // Producto
                celda('cantidad', $productoxcomprobante['stockactual'], '7', false, false, ' id="stockactual"');
                celda('iva', $productoxcomprobante['iva'], '7');
                celda('moneda', $productoxcomprobante['costocombo'], '7', false, false, ' id="costocombo"', false, $productoxcomprobante['simbolo']);
                celda('moneda', $productoxcomprobante['importe'], '10', '7', false, ' id="importe"', false, $productoxcomprobante['simbolo']);
                break;
        }
        salto_linea();
        if ($discrimina != 'combo' && $discrimina != 'traslado') {
            bloque_inicio('entrada_'.$productoxcomprobante[$GLOBALS['idx']].'_oculta', 'class="oculto" style="display:none;"');
            {
                area('observacion', $i18n_funciones[71], $productoxcomprobante['observacion']);

                // TODO: Cuando vaya a salir para todos los usuarios, mejorar en diseño y i18n
                if ($_SESSION['modulo_trazabilidad'] && $productoxcomprobante['idproducto'] && campo_sql(consulta_sql("SELECT trazabilidad FROM productos WHERE idproducto = '".$productoxcomprobante['idproducto']."' LIMIT 1"), 0)) {
                    enlaces('', array(
                        array('tipo' => 'modal', 'valor' => 'Administrar trazabilidades', 'url' => 'trazabilidadesxventas', 'id' => $productoxcomprobante['idproductoxventa'])
                        ));
                }
            }
            bloque_fin();
        }
    }
    linea_fin(array(
        array('tipo' => 'ajax', 'url' => $url_mod, 'a' => 'ok', 'value' => $i18n_funciones[22]),
        array('tipo' => 'ajax', 'url' => $url_mod, 'a' => 'no', 'value' => $i18n_funciones[26])));
}

function comprobantes_alta_productoxcomprobante($discrimina, $idlista, $iddeposito)
{
    global $i18n_funciones;

    // TODO: Integrar de alguna forma al framework, si es posible como corresponde en un js
    if ($_SESSION['sistema_gratis']) {
        $selector_productos = '<div id="'.$GLOBALS['modulo'].'_seleccionador" class="entrada" style="width: ancho%;">
            <input type="text" autocomplete="off" class="seleccionar_input_gratis" name="'.$GLOBALS['modulo'].'_input" id="'.$GLOBALS['modulo'].'_input_gratis" value="" maxlength="200" onkeypress="var charCode = event.keyCode || event.which; if (charCode == 13) return false;" style="width: 80%;" placeholder="" tabindex="1">
            <div class="buscar_botones gratis">
                <a href="#" onclick="gratis()" id="'.$GLOBALS['modulo'].'_buscar_gratis"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/buscar.png" title="'.$i18n_funciones[17].'"></a>
                <a href="#" onclick="gratis()" id="'.$GLOBALS['modulo'].'_todo_gratis"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/todo.png" title="'.$i18n_funciones[18].'"></a>
            </div>
        </div>
        <div id="'.$GLOBALS['modulo'].'_seleccionado" class="celda" style="width: ancho%; display: none;"></div>';
    } else {
        $selector_productos = '<div id="'.$GLOBALS['modulo'].'_seleccionador" class="entrada" style="width: ancho%;">
            <input type="text" autocomplete="off" class="seleccionar_input" name="'.$GLOBALS['modulo'].'_input" id="'.$GLOBALS['modulo'].'_input" value="" maxlength="200" onkeypress="var charCode = event.keyCode || event.which; if (charCode == 13) return false;" style="width: 80%;" placeholder="Descripción, código, o rubro..." tabindex="1">
            <div class="buscar_botones">
                <a href="#" id="'.$GLOBALS['modulo'].'_buscar"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/buscar.png" title="'.$i18n_funciones[17].'"></a>
                <a href="#" id="'.$GLOBALS['modulo'].'_todo"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/todo.png" title="'.$i18n_funciones[18].'"></a>
            </div>
        </div>
        <div id="'.$GLOBALS['modulo'].'_seleccionado" class="celda" style="width: ancho%; display: none;"></div>';
    }

    linea_inicio('fila', 2, false, 'id="linea_agregar"');
    {
        entrada('hidden', 'idlista', '', $idlista);
        entrada('hidden', 'iddeposito', '', $iddeposito);
        entrada('hidden', 'idproducto', '', '0');
        if ($discrimina != 'combo' && $discrimina != 'traslado')
            celda('imagen', $i18n_funciones[20], 'imagen', 'vermas', 'vermas');
        switch ($discrimina) {
            case 'A':
                celda('texto', '', '15', false, false, 'id="codigo_nuevo"');
                entrada('cantidad', 'cantidad', '', '1', '7', false, false, 'tabindex="4"');
                echo str_replace('ancho', '38', $selector_productos);
                if ($GLOBALS['modulo'] == 'ventas' && (!$_SESSION['perfil_ventas_nuevo'] && !$_SESSION['perfil_ventas_mod_precios'])) {
                    entrada('hidden', 'precio', '', '', false, false, false, 'id="precio"');
                    celda('moneda', '', '10', false, false, 'id="precio_no" tabindex="5"'); //?
                    celda('iva', '', '5', false, false, 'tabindex="6"');
                    entrada('descuento', 'descuento', false, false, '7', '', false, 'disabled="disabled"');

                } else {
                    entrada('hidden', 'precio', '', '', false, false, false, 'id="precio"');
                    entrada('moneda', 'precio', '', '', '10', false, false, 'tabindex="5"');
                    selector('idiva', '', '5', '7', 'tablas_ivas', false, false, false, false, false, 'tabindex="6"');
                    entrada('descuento', 'descuento', false, false, '7', '', false, 'tabindex="7"');
                }
                celda('texto', '', '10', false, false, 'id="importe_nuevo"');
                break;

            case 'B':
                celda('texto', '', '15', false, false, 'id="codigo_nuevo"');
                entrada('cantidad', 'cantidad', '', '1', '7', false, false, 'tabindex="4"');
                echo str_replace('ancho', '38', $selector_productos);
                if ($GLOBALS['modulo'] == 'ventas' && (!$_SESSION['perfil_ventas_nuevo'] && !$_SESSION['perfil_ventas_mod_precios'])) {
                    entrada('hidden', 'preciofinal', '', '', false, false, false, 'id="preciofinal"');
                    celda('iva', '', '5', false, false, 'tabindex="5"');
                    texto('moneda', '', '0', '10', false, false, false, 'id="preciofinal" tabindex="6"'); //?
                    entrada('descuento', 'descuento', false, false, '7', '', false, 'disabled="disabled"');
                } else {
                    selector('idiva', '', '5', '7', 'tablas_ivas', false, false, false, false, false, 'tabindex="5"');
                    entrada('moneda', 'preciofinal', '', '', '10', false, false, 'tabindex="6"'); //?
                    entrada('descuento', 'descuento', false, false, '7', '', false, 'tabindex="7"');
                }
                celda('texto', '', '10', false, false, 'id="importe_nuevo"');
                break;

            case 'C':
                celda('texto', '', '15', false, false, 'id="codigo_nuevo"');
                entrada('cantidad', 'cantidad', '', '1', '7', false, false, 'tabindex="4"');
                echo str_replace('ancho', '48', $selector_productos);
                if ($GLOBALS['modulo'] == 'ventas' && (!$_SESSION['perfil_ventas_nuevo'] && !$_SESSION['perfil_ventas_mod_precios'])) {
                    entrada('hidden', 'precio', '', '', false, false, false, 'id="precio"');
                    celda('moneda', '', '10', false, false, 'id="precio_no" tabindex="5"'); //?
                    entrada('descuento', 'descuento', false, false, '7', '', false, 'disabled="disabled"');

                } else {
                    entrada('moneda', 'precio', '', '', '10', false, false, 'tabindex="6"'); //?
                    entrada('descuento', 'descuento', false, false, '7', '', false, 'tabindex="7"');
                }
                celda('texto', '', '10', false, false, 'id="importe_nuevo"');
                break;

            case 'R':
            case 'traslado':
                celda('texto', '', '15', false, false, 'id="codigo_nuevo"');
                entrada('cantidad', 'cantidad', '', '1', '7', false, false, 'tabindex="4"');
                echo str_replace('ancho', '70', $selector_productos);
                break;

            case 'combo':
                celda('texto', '', '10', false, false, 'id="codigo_nuevo"');
                entrada('cantidad', 'cantidad', '', '1', '7', false, false, 'tabindex="4"');
                echo str_replace('ancho', '38', $selector_productos);
                celda('cantidad', '', '7', false, false, 'id="stockactual"');
                celda('iva', '', '7', false, false, 'id="iva"');
                celda('moneda', '', '7', false, false, 'id="costo"'); //?
                if ($_SESSION['configuracion_discrimina'])
                    celda('moneda', '', '10', false, false, 'id="preciofinal"'); //?
        }
        salto_linea();
        if ($discrimina != 'combo' && $discrimina != 'traslado') {
            bloque_inicio('linea_agregar_oculta', 'class="oculto" style="display:none;"');
            {
                area('observacion', $i18n_funciones[71], '');
            }
            bloque_fin();
        }
    }
    $url_alta = $discrimina == 'traslado'
        ? 'productosxtraslados.php?a=alta'
        : 'productosx'.$GLOBALS['modulo'].'.php?a=alta';
    linea_fin(array(
        array(),
        array('tipo' => 'ajax', 'url' => $url_alta, 'a' => 'alta', 'title' => $i18n_funciones[27], 'opciones' => 'tabindex="8"')
        ));

    linea_inicio('fila', 3, false, 'id="linea_pensando" style="display:none;"');
    linea_fin();

    bloque_inicio($GLOBALS['modulo'].'_resultados', 'class="buscar_resultados"');
    bloque_fin();
}

function comprobantes_listar_titulos_productoxcomprobante($discrimina)
{
    global $i18n_funciones;

    switch ($discrimina) {
        case 'A':
            $titulos = array(
                'codigo' => $i18n_funciones[172],
                'cantidad' => $i18n_funciones[173],
                'descripcion' => $i18n_funciones[174],
                'precio' => $i18n_funciones[175],
                'iva' => $i18n_funciones[178],
                'descuento' => $i18n_funciones[179],
                'total' => $i18n_funciones[177],
                );
            break;

        case 'B':
            $titulos = array(
                'codigo' => $i18n_funciones[172],
                'cantidad' => $i18n_funciones[173],
                'descripcion' => $i18n_funciones[174],
                'preciofinal' => $i18n_funciones[175],
                'descuento' => $i18n_funciones[179],
                'total' => $i18n_funciones[177],
                );
            break;

        case 'C':
            $titulos = array(
                'codigo' => $i18n_funciones[172],
                'cantidad' => $i18n_funciones[173],
                'descripcion' => $i18n_funciones[174],
                'precio' => $i18n_funciones[175],
                'descuento' => $i18n_funciones[179],
                'total' => $i18n_funciones[177],
                );
            break;

        case 'R':
        case 'traslado':
            $titulos = array(
                'codigo' => $i18n_funciones[172],
                'cantidad' => $i18n_funciones[173],
                'descripcion' => $i18n_funciones[174],
                );
            break;

        case 'combo':
            $titulos = array(
                'codigo' => $i18n_funciones[172],
                'cantidad' => $i18n_funciones[173],
                'descripcion' => $i18n_funciones[174],
                'stock' => $i18n_funciones[181],
                'costo' => $i18n_funciones[178],
                'precio_sin_iva' => $i18n_funciones[179],
                );
            if ($_SESSION['configuracion_discrimina']) {
                $titulos['iva'] = $i18n_funciones[178];
                $titulos['precio'] = $i18n_funciones[175];
            }
            break;
    }
    return $titulos;
}

function comprobantes_listar_productoxcomprobante($productoxcomprobante, $discrimina)
{
    $productoxcomprobante['importe'] = comprobantes_importe($productoxcomprobante, $discrimina);
    if ($productoxcomprobante['idunidad'] && $productoxcomprobante['idunidad'] != 7)
        $productoxcomprobante['nombre'].= ' ('.$productoxcomprobante['unidad'].')';
    $productoxcomprobante['descuento'] = $productoxcomprobante['descuento'] != '0.00'
        ? $productoxcomprobante['descuento'].' %'
        : '';

    switch ($discrimina) {
        case 'A':
            $camposxproducto = array(
                'codigo' => $productoxcomprobante['codigo'],
                'cantidad' => convertir_numero($productoxcomprobante['cantidad'], 'cantidad'),
                'descripcion' => $productoxcomprobante['nombre'],
                'precio' => convertir_numero($productoxcomprobante['precio'], 'moneda', $productoxcomprobante['simbolo']),
                'iva' => $productoxcomprobante['iva'],
                'descuento' => convertir_numero($productoxcomprobante['descuento'], 'porcentaje'),
                'total' => convertir_numero($productoxcomprobante['importe'], 'moneda', $productoxcomprobante['simbolo']),
                );
            if ($productoxcomprobante['observacion'])
                $camposxproducto['observacion'] = $productoxcomprobante['observacion'];
            break;

        case 'B':
            $camposxproducto = array(
                'codigo' => $productoxcomprobante['codigo'],
                'cantidad' => convertir_numero($productoxcomprobante['cantidad'], 'cantidad'),
                'descripcion' => $productoxcomprobante['nombre'],
                'preciofinal' => convertir_numero($productoxcomprobante['preciofinal'], 'moneda', $productoxcomprobante['simbolo']),
                'descuento' => convertir_numero($productoxcomprobante['descuento'], 'porcentaje'),
                'total' => convertir_numero($productoxcomprobante['importe'], 'moneda', $productoxcomprobante['simbolo']),
                );
            if ($productoxcomprobante['observacion'])
                $camposxproducto['observacion'] = $productoxcomprobante['observacion'];
            break;

        case 'C':
            $camposxproducto = array(
                'codigo' => $productoxcomprobante['codigo'],
                'cantidad' => convertir_numero($productoxcomprobante['cantidad'], 'cantidad'),
                'descripcion' => $productoxcomprobante['nombre'],
                'precio' => convertir_numero($productoxcomprobante['precio'], 'moneda', $productoxcomprobante['simbolo']),
                'descuento' => convertir_numero($productoxcomprobante['descuento'], 'porcentaje'),
                'total' => convertir_numero($productoxcomprobante['importe'], 'moneda', $productoxcomprobante['simbolo']),
                );
            if ($productoxcomprobante['observacion'])
                $camposxproducto['observacion'] = $productoxcomprobante['observacion'];
            break;

        case 'R':
        case 'traslado':
            $camposxproducto = array(
                'codigo' => $productoxcomprobante['codigo'],
                'cantidad' => convertir_numero($productoxcomprobante['cantidad'], 'cantidad'),
                'descripcion' => $productoxcomprobante['nombre'],
                );
            if ($productoxcomprobante['observacion'])
                $camposxproducto['observacion'] = $productoxcomprobante['observacion'];
            break;

        case 'combo':
            break;
    }

    return $camposxproducto;
}

function comprobantes_listar_totales($comprobante, $discrimina)
{
    global $i18n;
    global $conexion;

    $tabla = 'ivasxcompras';
    $tabla_id = 'idcompra';
    if ($GLOBALS['modulo'] == 'ventas') {
        $tabla = 'ivasxventas';
        $tabla_id = 'idventa';
    }

    switch ($discrimina) {
        case 'A':
            $comprobante['totalnetos'] = redondeo($comprobante['neto'] + $comprobante['nogravado'] + $comprobante['exento']);
            $comprobante['totalivas'] = redondeo($comprobante['iva']);
            $comprobante['descuentoenpesos'] = redondeo($comprobante['subtotal'] - $comprobante['totalnetos']);
            $totales = array(
                'subtotal' => array('nombre' => 'Subtotal', 'valor' => convertir_numero($comprobante['subtotal'], 'moneda', $comprobante['simbolo'])),
                'descuento' => array('nombre' => 'Descuento (' . convertir_numero($comprobante['descuento'], 'porcentaje') . ')',
                'valor' => convertir_numero($comprobante['descuentoenpesos'], 'moneda', $comprobante['simbolo'])),
                'neto' => array('nombre' => $i18n[198], 'valor' => convertir_numero($comprobante['neto'], 'moneda', $comprobante['simbolo'])),
                'nogravado' => array('nombre' => $i18n[199], 'valor' => convertir_numero($comprobante['nogravado'], 'moneda', $comprobante['simbolo'])),
                'exento' => array('nombre' => $i18n[200], 'valor' => convertir_numero($comprobante['exento'], 'moneda', $comprobante['simbolo'])),
                'tributos' => array('nombre' => $i18n[194], 'valor' => convertir_numero($comprobante['tributos'], 'moneda', $comprobante['simbolo'])),
                'neto' => array('nombre' => $i18n[198], 'valor' => convertir_numero($comprobante['neto'], 'moneda', $comprobante['simbolo'])),
                //'total' => array('nombre' => 'TOTAL', 'valor' => convertir_numero($comprobante['total'], 'moneda')),
                );
            //agregar los ivas de la venta x Lucas
            $resultado_sql = consulta_sql(
                "SELECT nombre, iva, campo
                FROM ".$tabla." t
                JOIN tablas_ivas on  t.idiva = tablas_ivas.idiva
                WHERE ".$tabla_id ."= ".$comprobante[$tabla_id], $conexion);
            if (contar_sql($resultado_sql)) {
                while ($ivas = array_sql($resultado_sql)) {
                    $totales[$ivas['campo']] = array('nombre' => 'IVA '.floatval($ivas['nombre']).'%', 'valor' => convertir_numero($ivas['iva'], 'moneda', $comprobante['simbolo'])); //?
                }
            }
            $totales['total']= array('nombre' => 'TOTAL', 'valor' => convertir_numero($comprobante['total'], 'moneda', $comprobante['simbolo']));
            break;

        case 'B':
            $comprobante['totalnetos'] = redondeo($comprobante['neto'] + $comprobante['nogravado'] + $comprobante['exento']);
            $comprobante['totalivas'] = campo_sql(consulta_sql(
                "SELECT SUM(iva)
                FROM ivasxventas WHERE idventa = '".$comprobante['idventa']."'
               ", $conexion));
            //$comprobante['totalivas'] = redondeo($comprobante['iva']);
            $comprobante['descuentoenpesos'] = redondeo($comprobante['subtotal'] - $comprobante['totalnetos'] - $comprobante['totalivas']);
            $totales = array(
                'subtotal' => array('nombre' => 'Subtotal', 'valor' => convertir_numero($comprobante['subtotal'], 'moneda', $comprobante['simbolo'])),
                'descuento' => array('nombre' => 'Descuento (' . convertir_numero($comprobante['descuento'], 'porcentaje') . ')',
                'valor' => convertir_numero($comprobante['descuentoenpesos'], 'moneda', $comprobante['simbolo'])),
                // 'tributos' => array('nombre' => $i18n[194], 'valor' => convertir_numero($comprobante['tributos'], 'moneda', $comprobante['simbolo'])),
                'total' => array('nombre' => 'TOTAL', 'valor' => convertir_numero($comprobante['total'], 'moneda', $comprobante['simbolo'])),
                );
            break;

        case 'C':
            $comprobante['descuentoenpesos'] = $GLOBALS['modulo'] == 'ventas'
                ? redondeo($comprobante['subtotal'] - $comprobante['neto'])
                : redondeo($comprobante['subtotal'] - $comprobante['nogravado']);
            $totales = array(
                'subtotal' => array('nombre' => 'Subtotal', 'valor' => convertir_numero($comprobante['subtotal'], 'moneda', $comprobante['simbolo'])),
                'descuento' => array('nombre' => 'Descuento (' . convertir_numero($comprobante['descuento'], 'porcentaje') . ')',
                'valor' => convertir_numero($comprobante['descuentoenpesos'], 'moneda', $comprobante['simbolo'])),
                'total' => array('nombre' => 'TOTAL', 'valor' => convertir_numero($comprobante['total'], 'moneda', $comprobante['simbolo'])),
                );
            break;

        case 'R':
        case 'traslado':
            break;

        case 'combo':
            $comprobante['descuentoenpesos'] = $GLOBALS['modulo'] == 'ventas'
                ? redondeo($comprobante['subtotal'] - $comprobante['neto'])
                : redondeo($comprobante['subtotal'] - $comprobante['nogravado']);
            $totales = array(
                'subtotal' => array('nombre' => 'Subtotal', 'valor' => convertir_numero($comprobante['subtotal'], 'moneda', $comprobante['simbolo'])),
                'descuento' => array('nombre' => 'Descuento (' . convertir_numero($comprobante['descuento'], 'porcentaje') . ')',
                    'valor' => convertir_numero($comprobante['descuentoenpesos'], 'moneda', $comprobante['simbolo'])),
                'total' => array('nombre' => 'TOTAL', 'valor' => convertir_numero($comprobante['total'], 'moneda', $comprobante['simbolo'])),
                );
            break;
    }

    if ($comprobante['descuentoenpesos'] == 0)
        unset($totales['descuento']);

    return $totales;
}

function comprobantes_actualizar_precios_venta($idventa)
{
    $idlista = campo_sql(consulta_sql("SELECT idlista FROM ventas WHERE idventa = '".$idventa."'"));
    $pagacosto = campo_sql(consulta_sql("SELECT pagacosto FROM categorias_clientes WHERE idtipocliente = (SELECT idtipocliente FROM clientes WHERE idcliente = (SELECT idcliente FROM ventas WHERE idventa = '$idventa'))"));

    consulta_sql(
        "UPDATE productosxventas AS pv
            INNER JOIN productos AS p ON p.idproducto = pv.idproducto
            INNER JOIN precios ON pv.idproducto = precios.idproducto AND precios.idlista = '".$idlista."'
        SET
            pv.precio = ".($pagacosto ? 'p.costo' : 'precios.precio').",
            pv.preciofinal = ".($pagacosto ? 'p.costo' : 'precios.preciofinal').",
            pv.costo = p.costo
        WHERE pv.idproducto > 0
            AND idventa = '$idventa'");
}

function comprobantes_actualizar_costos_compra($idcompra)
{
    $cotizaciones = [];
    $idmoneda_compra = campo_sql(consulta_sql("SELECT idmoneda FROM compras WHERE idcompra = '$idcompra'"));

    $productosxcompra_sql = consulta_sql(
        "SELECT productosxcompras.idproductoxcompra, productos.idmoneda, productos.costo
        FROM productosxcompras
            JOIN productos ON productos.idproducto = productosxcompras.idproducto
        WHERE productosxcompras.idproducto > 0
            AND idcompra = '$idcompra'");

    if (contar_sql($productosxcompra_sql) == 0)
        return;

    while ($productosxcompra = array_sql($productosxcompra_sql)) {
        $cotizaciones[] = [
            'id' => $productosxcompra['idproductoxcompra'],
            'idmoneda_destino' => $idmoneda_compra,
            'idmoneda_origen' => $productosxcompra['idmoneda'],
            'valor' => $productosxcompra['costo'],
        ];
    }
    $actualizar_cotizaciones = cotizaciones($cotizaciones);

    $update_when = [];
    $update_where = [];
    foreach ($actualizar_cotizaciones as $idproductoxcompra => $costo) {
        $update_when[] = "WHEN idproductoxcompra = '".$idproductoxcompra."' THEN '".$costo."'";
        $update_where[] = $idproductoxcompra;
    }
    consulta_sql(
        "UPDATE productosxcompras
        SET costo = CASE ".implode(' ', $update_when)." END
        WHERE idproductoxcompra IN (".implode(',', $update_where).")");

}

function comprobantes_actualizar_precios_productos($idcompra, $modo_ajuste)
{
    global $i18n;

    $idmoneda_compra = campo_sql(consulta_sql("SELECT idmoneda FROM compras WHERE idcompra = '$idcompra'"));
    $productosxcompra_sql = consulta_sql(
        "SELECT pc.idproductoxcompra, productos.idmoneda, productos.costo, productos.idproducto,
            (SELECT pc.costo * (1 - pc.descuento/100) * (1 - compras.descuento/100)) AS costo_final
        FROM productosxcompras AS pc
            JOIN productos ON productos.idproducto = pc.idproducto
            JOIN compras ON pc.idcompra = compras.idcompra
        WHERE pc.idproducto > 0
            AND pc.idcompra = '$idcompra'");

    if (!contar_sql($productosxcompra_sql))
        return;

    while ($productosxcompra = array_sql($productosxcompra_sql)) {
        $cotizaciones[] = [
            'id' => $productosxcompra['idproducto'],
            'idmoneda_destino' => $productosxcompra['idmoneda'],
            'idmoneda_origen' => $idmoneda_compra,
            'valor' => $productosxcompra['costo_final'],
        ];
    }
    $actualizar_cotizaciones = cotizaciones($cotizaciones);

    $update_when = [];
    $update_where = [];
    foreach ($actualizar_cotizaciones as $idproducto => $costo) {
        $update_when[] = "WHEN idproducto = '".$idproducto."' THEN '".$costo."'";
        $update_where[] = $idproducto;
    }
    consulta_sql(
        "UPDATE productos
        SET costo = CASE ".implode(' ', $update_when)." END
        WHERE idproducto IN (".implode(',', $update_where).")");


    switch ($modo_ajuste) {
        default:
            $sql_precio = $sql_preciofinal = $sql_utilidad = "";
            break;

        case 'costo_precio':
            $sql_precio = "{costo_final} * (p.utilidad / 100 + 1)";
            $sql_preciofinal = ($_SESSION['configuracion_discrimina']
                ? "(({costo_final}) * (p.utilidad / 100 + 1)) * (1 + (SELECT valor FROM tablas_ivas WHERE tablas_ivas.idiva = productos.idiva) / 100) "
                : "");
            break;

        case 'costo_utilidad':
            $sql_utilidad = "((p.precio / ({costo_final}) - 1) * 100)";
            break;

    }

    if ($sql_precio || $sql_preciofinal || $sql_utilidad) {

        $listas = consulta_sql("SELECT idlista, idmoneda FROM listas");
        $update_precios = [];
        $update_preciosfinales = [];
        $update_utilidades = [];
        while ($lista = array_sql($listas)) {
            puntero_sql($productosxcompra_sql);
            while ($productosxcompra = array_sql($productosxcompra_sql)) {
                $costo_convertido_lista = cotizacion($lista['idmoneda'], $idmoneda_compra, $productosxcompra['costo_final']);
                if ($sql_precio)
                    $update_precios[] = "WHEN p.idproducto = '".$productosxcompra['idproducto']."' AND idlista = '".$lista['idlista']."' THEN ".str_replace("{costo_final}", $costo_convertido_lista, $sql_precio);
                if ($sql_preciofinal)
                    $update_preciosfinales[] = "WHEN p.idproducto = '".$productosxcompra['idproducto']."' AND idlista = '".$lista['idlista']."' THEN ".str_replace("{costo_final}", $costo_convertido_lista, $sql_preciofinal);
                if ($sql_utilidad)
                    $update_utilidades[] = "WHEN p.idproducto = '".$productosxcompra['idproducto']."' AND idlista = '".$lista['idlista']."' THEN ".str_replace("{costo_final}", $costo_convertido_lista, $sql_utilidad);
            }
        }

        if (!empty($update_precios) || !empty($update_preciosfinales) || !empty($update_utilidades)) {
            $update_where_values = implode(',', $update_where);

            if (!empty($update_precios) && !empty($update_preciosfinales))
                $update_sql = "precio = CASE ".implode(' ', $update_precios)." END, preciofinal = CASE ".implode(' ', $update_preciosfinales)." END";
            else if (!empty($update_precios))
                $update_sql = "precio = CASE ".implode(' ', $update_precios)." END";
            else if (!empty($update_utilidades))
                $update_sql = "utilidad = CASE ".implode(' ', $update_utilidades)." END";

            consulta_sql(
                "UPDATE precios AS p
                    LEFT JOIN productos ON p.idproducto = productos.idproducto
                SET ".$update_sql."
                WHERE p.idproducto IN ($update_where_values)");
        }

        // Busco si algún producto quedó con utilidad negativa
        $resultado_sql = consulta_sql(
            "SELECT productos.idproducto, nombre
            FROM productos
            INNER JOIN precios ON productos.idproducto = precios.idproducto
            WHERE productos.idproducto IN
                (SELECT productosxcompras.idproducto
                FROM productosxcompras
                WHERE idcompra = ".$idcompra." AND productosxcompras.idproducto > 0)
                AND precios.utilidad < 0");
        if (contar_sql($resultado_sql)) {
            while ($producto = array_sql($resultado_sql)) {
                mensajes_alta($i18n[111].'<a href=productos.php?a=ver&id='.$producto['idproducto'].'>'.$producto['nombre'].'</a>');
            }
        }
    }
}

function comprobantes_movimientos($comprobante, $editable = false) {
    global $i18n;
    global $modulo;

    texto('texto', $i18n[221], false);
    // Se generarán y es editable
    if ($comprobante['estado'] == 'abierto' && $editable) {
        $marcas = array(
            array('nombre' => 'muevestock', 'valor' => $comprobante['muevestock'], 'titulo' => $i18n[232]),
        );
        if ($comprobante['discrimina'] != 'R') {
            $marcas[] = array('nombre' => 'muevesaldo', 'valor' => $comprobante['muevesaldo'], 'titulo' => $i18n[234]);
            marcas('', 'auto', $marcas);
        } else {
            marcas('', 'auto', $marcas);
            texto('texto', false, $i18n[235]);
        }
        texto('texto', false, ($comprobante['esfiscal'] ? $i18n[236] : $i18n[237]), 'auto', false, false, $i18n[231]);
        if ($comprobante['operacioninversa'])
            texto('italica', false, $i18n[229]);

    // Se generarán, pero no tengo permiso
    } elseif ($comprobante['estado'] == 'abierto') {
        texto('texto', false, ($comprobante['muevestock'] ? $i18n[232] : $i18n[233]));
        entrada('hidden', 'muevestock', false, $comprobante['muevestock']);
        texto('texto', false, ($comprobante['muevesaldo'] ? $i18n[234] : $i18n[235]));
        entrada('hidden', 'muevesaldo', false, $comprobante['muevesaldo']);
        texto('texto', false, ($comprobante['esfiscal'] ? $i18n[236] : $i18n[237]), 'auto', false, false, $i18n[231]);
        if ($comprobante['operacioninversa'])
            texto('italica', false, $i18n[229]);
    // Ya se generaron
    } else {
        texto('texto', false, ($comprobante['muevestock'] ? $i18n[222] : $i18n[223]));
        entrada('hidden', 'muevestock', false, $comprobante['muevestock']);
        texto('texto', false, ($comprobante['muevesaldo'] ? $i18n[224] : $i18n[225]));
        entrada('hidden', 'muevesaldo', false, $comprobante['muevesaldo']);
        texto('texto', false, ($comprobante['esfiscal'] ? $i18n[226] : $i18n[227]), 'auto', false, false, $i18n[231]);
        if ($comprobante['operacioninversa'])
            texto('italica', false, $i18n[228]);
    }

    if ($comprobante['muevestock']) {
        contenido_inicio($i18n[363], '100', false, false, false, 'style="margin-left: -10px;"', 'mini-icono-historial-producto');
        {
            enlaces('', array(
                array('tipo' => 'flotante', 'modulo' => $modulo, 'url' => 'productos_log', 'id' => ($modulo == 'ventas' ? $comprobante['idventa'] : $comprobante['idcompra']), 'valor' => $i18n[364])),'100');
        }
        contenido_fin();
    }

}

//Funciones de refactorización ventas_mod y compras_mod

function inicializar($boton, $id, $datos) {
    global $modulo;

    if ($boton) {
        if ($modulo == 'ventas') {
            $venta = array_sql(
                consulta_sql("SELECT estado, numero, fecha, idusuario, situacion, muevesaldo, muevestock, idtipoventa
                    FROM ventas
                    WHERE idventa = '$id'
                    LIMIT 1"
                ));
            $datos['estado'] = $venta['estado'];
            if (!$_SESSION['perfil_ventas_mod_todos']) {
                $datos['numero'] = $venta['numero'];
                $datos['fecha'] = mostrar_fecha('fechayhora', $venta['fecha']);
                $datos['idusuario'] = $venta['idusuario'];
                $datos['situacion'] = $venta['situacion'];
            }
            if (!$_SESSION['perfil_configuraciones_empresa']) {
                $datos['muevesaldo'] = $venta['muevesaldo'];
                $datos['muevestock'] = $venta['muevestock'];
            }
            if (!$datos['fecha'])
                $datos['fecha'] = date("d-m-Y H:i");
            if (!$datos['numero'])
                $datos['numero'] = $venta['numero'];

        } else { // $modulo == 'compras'
            $periodoimputacion = explode('-', $datos['periodoimputacion']);
            $datos['fechaimputacion'] = $periodoimputacion[1].'-'.$periodoimputacion[0].'-01';
        }

    } else { // !$boton
        $datos = array(
            'agregarpago' => ($modulo == 'ventas' ? $_SESSION['control_ventas_agregarpago'] : true), //ver compras
            'idconcepto' => ($modulo == 'ventas' ? $_SESSION['control_ultimoconceptoventa'] : $_SESSION['control_ultimoconceptocompra']),
            'idformapago' => ($modulo == 'ventas' ? $_SESSION['control_ultimoformapagoventa'] : $_SESSION['control_ultimoformapagocompra']),
            'idcaja' => campo_sql(consulta_sql(
                "SELECT idcaja
                FROM categorias_cajas
                WHERE idtipocaja = (SELECT ". ($modulo == 'ventas' ? 'ultimotipocajaventa' : 'ultimotipocajacompra')." FROM controles WHERE idusuario = '".$_SESSION['usuario_idusuario']."')
                LIMIT 1"), 0),
            );
    }

    if ($modulo == 'ventas') {

        $datos['idventa'] = $id;
        $tipocliente = array_sql(consulta_sql("SELECT categorias_clientes.idlista, categorias_clientes.cuentacorriente,
            categorias_clientes.maxcuentacorriente, categorias_clientes.pagacosto,
            categorias_clientes.condicion, clientes.nombre AS cliente
            FROM clientes
            INNER JOIN categorias_clientes ON clientes.idtipocliente = categorias_clientes.idtipocliente
            WHERE idcliente = (SELECT idcliente FROM ventas WHERE idventa = '".$id."')
            LIMIT 1")
        );
        if (!$datos['idlista'])
            $datos['idlista'] = $tipocliente['idlista']; // No podemos pisar si la venta se está cerrando

        $datos['cliente_cc']            = $tipocliente['cuentacorriente'];
        $datos['maxcuentacorriente']    = $tipocliente['maxcuentacorriente'];
        $datos['pagacosto']             = $tipocliente['pagacosto'];
        $datos['condicion']             = $tipocliente['condicion'];
        $datos['cliente']               = $tipocliente['cliente'];


    } else { // $modulo == 'compras'

        $compra = array_sql(consulta_sql(
            "SELECT razonsocial AS proveedor, estado, esfiscal, idproveedor, idtipocompra FROM compras WHERE idcompra = $id"));
        $datos['idcompra'] = $id;
        $datos['estado'] = $compra['estado'];
        $datos['esfiscal'] = $compra['esfiscal'];
        $datos['idproveedor'] = $compra['idproveedor'];
        $datos['idtipocompra'] = $compra['idtipocompra'];
        $datos['proveedor'] = $compra['proveedor'];

        $tipoproveedor = array_sql(consulta_sql("
            SELECT condicion, iddeposito
            FROM categorias_proveedores
            WHERE idtipoproveedor = (SELECT idtipoproveedor FROM proveedores WHERE idproveedor = '{$datos['idproveedor']}')
            "));
        //$datos['iddeposito'] = $tipoproveedor['iddeposito'];
        $datos['condicion'] = $tipoproveedor['condicion'];

        if ($compra['esfiscal']) {
            $tipocompra = array_sql(consulta_sql("SELECT * FROM categorias_compras WHERE idtipocompra =".$datos['idtipocompra']));
            if ($comportamiento = array_sql(
                consulta_sql("SELECT * FROM tablas_comportamientos WHERE nombre like '".$tipocompra["nombre"]." ".$datos['letra']."'"))) {
                $datos['idcomportamiento']=$comportamiento["idcomportamiento"];
            }
            $datos['numero_compras_proveedor'] = $datos["letra"].completar_numero($datos['puntodeventa'], 5)."-".completar_numero($datos["numero"], 9);
            $datos['numerocompleto'] = $datos['numero_compras_proveedor'];

        } else { // !$datos['esfiscal']
            $datos['numero_compras_proveedor'] = $datos['numerocompleto'];
            $datos['numero'] = 0;
            $datos['puntodeventa'] = 0;
            $datos['idcomportamiento'] = 0;
        }

    }
    return $datos;
}

function inicializar_compra($id) {
    $compra = array_sql(consulta_sql(
        "SELECT compras.*,
            usuarios.nombre AS usuario,
            categorias_compras.nombre AS tipocompra,
            tablas_comportamientos.nombre AS comportamiento,
            tablas_condiciones.nombre AS tipoiva,
            categorias_localidades.nombre AS localidad,
            depositos.nombre AS deposito,
            CASE WHEN tablas_comportamientos.nombre is null THEN '' ELSE right(tablas_comportamientos.nombre,1) END AS letra, CASE WHEN categorias_compras.esfiscal = '1' THEN CONCAT(lpad(compras.puntodeventa,5,'0'),'-',lpad(compras.numero,8,'0'))  ELSE compras.numerocompleto END AS nro_compra,
            monedas.nombre AS moneda, monedas.simbolo
        FROM compras
            LEFT JOIN usuarios ON compras.idusuario = usuarios.idusuario
            LEFT JOIN categorias_compras ON compras.idtipocompra = categorias_compras.idtipocompra
            LEFT JOIN tablas_comportamientos ON  tablas_comportamientos.idcomportamiento = compras.idcomportamiento
            LEFT JOIN tablas_condiciones ON compras.idtipoiva = tablas_condiciones.idtipoiva
            LEFT JOIN categorias_localidades ON compras.idlocalidad = categorias_localidades.idlocalidad
            LEFT JOIN depositos ON compras.iddeposito = depositos.iddeposito
            LEFT JOIN monedas ON compras.idmoneda = monedas.idmoneda
        WHERE idcompra = '$id'
        LIMIT 1"
    ));
    if(!$compra['idlocalidad']){
        $localidad = array_sql(consulta_sql("SELECT configuraciones.idlocalidad, categorias_localidades.nombre AS localidad
            FROM configuraciones
            LEFT JOIN categorias_localidades ON configuraciones.idlocalidad = categorias_localidades.idlocalidad
            LIMIT 1"));
        $compra['idlocalidad'] = $localidad['idlocalidad'];
        $compra['localidad'] = $localidad['localidad'];
    }
    return $compra;
}

function inicializar_venta($id) {
    $venta = array_sql(consulta_sql(
    "SELECT ventas.*,
        usuarios.nombre AS usuario, tablas_condiciones.nombre AS tipoiva,
        categorias_ventas.discrimina, categorias_ventas.letra, categorias_ventas.puntodeventa, categorias_ventas.tipofacturacion, categorias_ventas.nombre, categorias_ventas.tienesituacion, categorias_ventas.operacioninversa AS operacioninversa,  categorias_localidades.nombre AS localidad,
            listas.nombre AS lista, depositos.nombre AS deposito,
            monedas.nombre AS moneda, monedas.simbolo
    FROM ventas
        LEFT JOIN usuarios ON ventas.idusuario = usuarios.idusuario
        LEFT JOIN tablas_condiciones ON ventas.idtipoiva = tablas_condiciones.idtipoiva
        LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
        LEFT JOIN categorias_localidades ON ventas.idlocalidad = categorias_localidades.idlocalidad
        LEFT JOIN listas ON ventas.idlista = listas.idlista
        LEFT JOIN depositos ON ventas.iddeposito = depositos.iddeposito
        LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
    WHERE idventa = '$id'
    LIMIT 1"));

    if (!$venta)
        return false;

    if (!$venta['idlocalidad']) {
        $localidad = array_sql(consulta_sql("SELECT configuraciones.idlocalidad, categorias_localidades.nombre AS localidad
            FROM configuraciones
            LEFT JOIN categorias_localidades ON configuraciones.idlocalidad = categorias_localidades.idlocalidad
            LIMIT 1"));
        $venta['idlocalidad'] = $localidad['idlocalidad'];
        $venta['localidad'] = $localidad['localidad'];
    }
    return $venta;
}

function inicializar_proveedor($idproveedor) {
    $proveedor = array_sql(consulta_sql(
        "SELECT proveedores.*,
            tablas_condiciones.nombre AS tipoiva,
            categorias_localidades.nombre AS localidad,
            monedas.simbolo
        FROM proveedores
            LEFT JOIN tablas_condiciones ON proveedores.idtipoiva = tablas_condiciones.idtipoiva
            LEFT JOIN categorias_localidades ON proveedores.idlocalidad = categorias_localidades.idlocalidad
            LEFT JOIN monedas ON proveedores.idmoneda = monedas.idmoneda
        WHERE idproveedor = '".$idproveedor."'
        LIMIT 1"
    ));
    return $proveedor;
}

function inicializar_cliente($idcliente) {
    $cliente =  array_sql(consulta_sql(
        "SELECT clientes.*,
            categorias_localidades.nombre AS localidad,
            categorias_clientes.idlista, categorias_clientes.pagacosto,
            monedas.simbolo
        FROM clientes
            LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
            LEFT JOIN categorias_clientes ON clientes.idtipocliente = categorias_clientes.idtipocliente
            LEFT JOIN monedas ON clientes.idmoneda = monedas.idmoneda
        WHERE idcliente = '".$idcliente."'
        LIMIT 1"
    ));
    return $cliente;
}

function obtener_operacion($id) {
    global $modulo;

    if ($modulo == 'ventas') {
        $resultado = array_sql(consulta_sql(
            "SELECT ventas.*, saldos.saldo, categorias_clientes.cuentacorriente AS cliente_cc, categorias_clientes.maxcuentacorriente,
                cv.iddeposito, cv.nombre AS tipoventa, cv.letra, cv.puntodeventa, cv.ultimonumero,
                cv.discrimina, cv.operacioninversa AS operacioninversa, cv.tipofacturacion, cv.idcomportamiento,
                monedas.nombre AS moneda, monedas.simbolo,
                clientes.idmoneda AS idmoneda_tercero
            FROM ventas
                INNER JOIN categorias_ventas AS cv ON ventas.idtipoventa = cv.idtipoventa
                LEFT JOIN saldos ON saldos.tiporelacion = 'clientes' AND saldos.idrelacion = ventas.idcliente
                LEFT JOIN clientes ON ventas.idcliente = clientes.idcliente
                LEFT JOIN categorias_clientes ON clientes.idtipocliente = categorias_clientes.idtipocliente
                LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
            WHERE idventa = '$id'"
        ));
    } elseif ($modulo == 'compras') {
        $resultado = array_sql(consulta_sql("SELECT compras.*, categorias_compras.nombre AS tipocompra, proveedores.nombre AS proveedor,
                monedas.nombre AS moneda, monedas.simbolo,
                proveedores.idmoneda AS idmoneda_tercero
            FROM compras
                LEFT JOIN categorias_compras ON compras.idtipocompra = categorias_compras.idtipocompra
                LEFT JOIN proveedores ON compras.idproveedor = proveedores.idproveedor
                LEFT JOIN monedas ON compras.idmoneda = monedas.idmoneda
            WHERE idcompra = '$id'"
        ));
    }
    return $resultado;
}

function validar_cierre($operacion, $datos) {
    global $i18n;
    global $modulo;
    global $modulo_singular;
    global $idmodulo;
    global $idx;
    global $idtercero;
    global $tercero;
    global $i18n_funciones;

    if ($modulo == 'ventas') {
        $sql_ventas = "SELECT idventa
            FROM ventas
            WHERE idtipoventa = '".$operacion['idtipoventa']."'
                AND numero = '".$datos['numero']."'
                AND idventa != '".$datos[$idmodulo]."'
            LIMIT 1";

    } elseif ($modulo == 'compras') {
        //La validación de esfiscal / no fiscal con la misma numeración ya queda validada con esta query
        if ($operacion['esfiscal']) {
            $sql_compras = "SELECT idcompra
                FROM compras
                WHERE idproveedor = '".$operacion['idproveedor']."'
                    AND idtipocompra = '".$operacion['idtipocompra']."'
                    AND numero = '".$datos['numero']."'
                    AND puntodeventa = '".$datos['puntodeventa']."'
                    AND idcompra != '".$datos[$idmodulo]."'
                    AND estado != 'abierto' AND estado != 'anulado'
                LIMIT 1";
        } else {
            $sql_compras = "SELECT idcompra
                FROM compras
                WHERE idproveedor = '".$operacion['idproveedor']."'
                    AND numerocompleto = '".$datos['numerocompleto']."'
                    AND idcompra != '".$datos[$idmodulo]."'
                    AND estado != 'abierto' AND estado != 'anulado'
                LIMIT 1";
        }
    }

    if ($operacion['estado'] == 'cerrado' || $operacion['estado'] == 'anulado') {
        logs(true, '', json_encode($i18n[190]));
        //Esta venta / compra ya se encuentra cerrada
        if (!$_SESSION['omitir_mensajes'])
            ir_ahora($modulo.'.php?a=ver&id='.$idmodulo, $i18n[190]);
        return false;

    } elseif (($modulo == 'ventas' && ($operacion['total'] <= '0' && $operacion['discrimina'] != 'R'))
        || ($modulo == 'compras' && ($operacion['total'] <= '0' && ($datos['muevesaldo'] || $operacion['esfiscal'])))) {
        //No se puede cerrar esta venta con total igual a cero
        mensajes_alta($i18n[112]);
        logs(true, '', json_encode($i18n[112]));
        return false;

    } elseif (($modulo == 'compras' && (!$operacion['esfiscal'] && !$datos['numerocompleto'])
            || ($operacion['esfiscal'] && (!$datos['numero'] || !intval($datos['numero']) > 0 || !$datos['puntodeventa'])))) {
            //El número o el punto de venta son incorrectos
            mensajes_alta($i18n[119]);
            logs(true, '', json_encode($i18n[119]));
            return false;

    } elseif (($modulo == 'ventas' && ($datos['numero'] && $operacion['numero'] != $datos['numero'] && contar_sql(consulta_sql($sql_ventas))))
        || ($modulo == 'compras' && $operacion['idproveedor'] > 1
            && contar_sql(consulta_sql($sql_compras)))) {
            // Ya existe una venta de ese tipo con este número
            mensajes_alta($i18n[123]);
            logs(true, '', json_encode($i18n[123]));
            return false;

    } elseif (($modulo == 'ventas' && ($datos['condicionventa'] == 'cuentacorriente' || $datos['condicionventa'] == 'cheque' || !$datos['agregarpago'])
        && $datos['muevesaldo']
        && !$operacion['operacioninversa']
        && !$datos['cliente_cc'])) {
        //El cliente no tiene habilitada una cuenta corriente
        mensajes_alta($i18n[116]);
        logs(true, '', json_encode($i18n[116]));
        return false;

    } elseif ($modulo == 'compras' && (!$datos['fecha'] || $datos['fecha'] == '0000-00-00 00:00')) {
        //La fecha de compra no puede estar vacía
        mensajes_alta($i18n[121]);
        logs(true, '', json_encode($i18n[121]));
        return false;

    } elseif ($datos['agregarpago'] && !$datos['idcaja']) {
        //No se puede agregar una recibo/orden de pago relacionada sin asignarle una caja
        mensajes_alta($i18n[29]);
        logs(true, '', json_encode($i18n[29]));
        return false;

    } elseif ($datos['agregarpago'] && !contar_sql(consulta_sql("SELECT idtipocaja FROM categorias_cajas WHERE estado = '1' AND idcaja = '".$datos['idcaja']."' LIMIT 1"))) {
        //La caja que seleccionó se encuentra cerrada
        mensajes_alta($i18n[79]);
        logs(true, '', json_encode($i18n[79]));
        return false;

    } elseif ($modulo == 'ventas' && !$_SESSION['perfil_ventas_nuevo'] && contar_sql(consulta_sql("SELECT nombre FROM productosxventas WHERE idventa = '".$operacion['idventa']."' AND idproducto = 0 LIMIT 1"))) {
        // No tiene permiso para agregar productos no registrados
        mensajes_alta($i18n[184]);
        logs(true, '', json_encode($i18n[184]));
        return false;

    } elseif (
        ($datos['vencimiento1'] && $datos['vencimiento1'] != "0000-00-00" && (strtotime($datos['vencimiento1']) < strtotime($datos['fecha']))) ||
        ($datos['vencimiento2'] && $datos['vencimiento2'] != "0000-00-00" && (strtotime($datos['vencimiento2']) < strtotime($datos['fecha'])))) {
        // Las fechas de vencimientos no pueden ser anteriores a la fecha de la venta
        mensajes_alta($i18n[141]);
        logs(true, '', json_encode($i18n[141]));
        return false;

    } elseif ($datos['vencimiento1'] && $datos['vencimiento2']
        && (strtotime($datos['vencimiento2']) < strtotime($datos['vencimiento1']))) {
        // El 2º vencimiento no puede ser anterior al 1º vencimiento
        mensajes_alta($i18n[152]);
        logs(true, '', json_encode($i18n[152]));
        return false;

    } elseif ($modulo == 'ventas' && $operacion['tipofacturacion'] == 'electronico'
            && ((!is_numeric($datos['concepto']) || ($datos['concepto'] < 1) || ($datos['concepto'] > 3))
            || ($datos['concepto'] > 1
                && (!$datos['vencimiento1'] || !$datos['fechainicio'] || !$datos['fechafin'])))) {
            //Si tiene servicios entre los conceptos a incluir el 1º vencimiento y el período de facturación son obligatorios
            mensajes_alta($i18n[241]);
            logs(true, '', json_encode($i18n[241]));
            return false;

    } elseif ($modulo == 'ventas' && $datos['fechainicio'] && $datos['fechafin']
            && (strtotime($datos['fechainicio']) >= strtotime($datos['fechafin']))) {
            //fechafin no puede ser antes que fechainicio
            mensajes_alta($i18n[282]);
            logs(true, '', json_encode($i18n[282]));
            return false;

    } elseif ($modulo == 'ventas' && $datos['fechainicio'] && !$datos['fechafin']) {
            //fechafin no puede ser antes que fechainicio
            mensajes_alta($i18n[260]);
            logs(true, '', json_encode($i18n[260]));
            return false;

    } elseif (isset($_SESSION['limite_'.$modulo]) && $_SESSION['limite_'.$modulo]
            && $_SESSION['limite_'.$modulo] < contar_sql(consulta_sql("SELECT fecha FROM $modulo WHERE fecha >= '".date("Y-m-1")." 00:00:00'"))) {
            mensajes_alta(str_replace('{{modulo}}', $modulo, $i18n_funciones[327]));
            return false;

    } elseif ($modulo == 'ventas' && $datos['muevestock']) {
        $resultado_sql_productosxventas = consulta_sql("SELECT
                productosxventas.idproducto,
                SUM(productosxventas.cantidad) AS total_cantidad,
                productos.nombre AS producto,
                productos.controlarstock,
                productos.stocknegativo,
                stock.stockactual,
                productos.combo,
                tablas_unidades.nombre AS unidad
            FROM productosxventas
                LEFT JOIN productos ON productosxventas.idproducto = productos.idproducto
                LEFT JOIN stock ON productos.idproducto = stock.idproducto AND stock.iddeposito = '".$datos['iddeposito']."'
                LEFT JOIN tablas_unidades ON productos.idunidad = tablas_unidades.idunidad
            WHERE idventa = '".$operacion['idventa']."'
            GROUP BY productosxventas.idproducto
        ");

        while ($productoxventa = array_sql($resultado_sql_productosxventas)) {
            if (!$operacion['operacioninversa']
                && !$productoxventa['combo']
                && $productoxventa['idproducto'] > 0
                && $productoxventa['controlarstock']
                && !$productoxventa['stocknegativo']
                && $productoxventa['stockactual'] < $productoxventa['total_cantidad']) {
                    mensajes_alta(htmlentities($i18n[93].$productoxventa['stockactual'].' '.$productoxventa['unidad'].$i18n[122].$productoxventa['producto'], ENT_QUOTES));
                    logs(true, '', json_encode(htmlentities($i18n[93].$productoxventa['stockactual'].' '.$productoxventa['unidad'].$i18n[122].$productoxventa['producto'], ENT_QUOTES)));
                    return false;
            }
            if(!$operacion['operacioninversa']
                && $productoxventa['combo']
                && !$productoxventa['stocknegativo']) {
                $resultado_sql_productosxcombo = consulta_sql("SELECT productosxcombos.idproductoxcombo, productos.idproducto, productosxcombos.cantidad, productos.nombre AS producto, stock.stockactual, productos.stocknegativo, productos.controlarstock, tablas_unidades.nombre
                    FROM productosxcombos
                    LEFT JOIN productos ON productosxcombos.idproducto = productos.idproducto
                    LEFT JOIN tablas_unidades ON productos.idunidad = tablas_unidades.idunidad
                    LEFT JOIN stock ON productos.idproducto = stock.idproducto AND stock.iddeposito = '".$datos['iddeposito']."'
                    WHERE productosxcombos.idcombo = '".$productoxventa['idproducto']."'
                ");
                while ($productosxcombo = array_sql($resultado_sql_productosxcombo)) {
                    if (!$productosxcombo['stocknegativo'] && $productosxcombo['controlarstock'] && $productosxcombo['stockactual'] < $productosxcombo['cantidad'] * ($productoxventa['cantidad'] - $productoxventa['stockactual'])) { //me olvidada de restar el que está en stock en combo
                        mensajes_alta(htmlentities($i18n[93].$productosxcombo['stockactual'].' '.$productosxcombo['unidad'].$i18n[122].$productosxcombo['producto'], ENT_QUOTES));
                        logs(true, '', json_encode(htmlentities($i18n[93].$productosxcombo['stockactual'].' '.$productosxcombo['unidad'].$i18n[122].$productosxcombo['producto'], ENT_QUOTES)));
                        return false;
                    }
                }
            }
        }
    }

    if (($modulo == 'ventas' && ($datos['condicionventa'] == 'cuentacorriente' || $datos['condicionventa'] == 'cheque' || !$datos['agregarpago'])
        && !$operacion['operacioninversa']
        && $datos['muevesaldo']
        && $datos['maxcuentacorriente'] != '0'
        && $operacion['saldo'] + $operacion['total'] > $datos['maxcuentacorriente'])) {
        //El total de la venta más el saldo del cliente es superior a los permitidos en la cuenta corriente
        mensajes_alta($i18n[117].' ($ '.$operacion['total'].') '.$i18n[118].' ($ '.redondeo($operacion['saldo']).') '.$i18n[119].' $ '.$datos['maxcuentacorriente'].$i18n[120]);
        logs(true, '', json_encode($i18n[117].' ($ '.$operacion['total'].') '.$i18n[118].' ($ '.redondeo($operacion['saldo']).') '.$i18n[119].' $ '.$datos['maxcuentacorriente'].$i18n[120]));
        return false;
    }

    if ($modulo == 'ventas' &&
        !contar_sql(consulta_sql("SELECT idlista FROM listas WHERE idlista = '".$datos['idlista']."'"))) {
        mensajes_alta($i18n_funciones[186]);
        logs(true, '', json_encode($i18n_funciones[186]));
        return false;
    }
    if (!contar_sql(consulta_sql("SELECT iddeposito FROM depositos WHERE iddeposito = '".$datos['iddeposito']."'"))) {
        mensajes_alta($i18n_funciones[187]);
        logs(true, '', json_encode($i18n_funciones[187]));
        return false;
    }

    return true;
}


function trazabilidad($operacion) {
    global $i18n;
    global $modulo;
    global $modulo_singular;
    global $idx;
    global $idmodulo;

    if ($_SESSION['modulo_trazabilidad']
        && contar_sql(consulta_sql(
            "SELECT ".$idx."
            FROM productosx".$modulo."
                LEFT JOIN productos ON productosx".$modulo.".idproducto = productos.idproducto
            WHERE ".$idmodulo." = '".$operacion[$idmodulo]."'
                AND productos.trazabilidad = '1'
                AND (NOT EXISTS (SELECT * FROM movimientosxtrazabilidades WHERE tiporelacion = '".$modulo_singular."' AND movimientosxtrazabilidades.idrelacion = productosx".$modulo.".".$idx.")
                     OR productosx".$modulo.".cantidad != (SELECT SUM(cantidad) FROM movimientosxtrazabilidades WHERE tiporelacion = '".$modulo_singular."' AND idrelacion = productosx".$modulo.".".$idx."))
            LIMIT 1"))) {
            mensajes_alta($i18n[296]);

            // PARCHE PARA CONTROLAR LA TRAZABILIDAD
            file_put_contents(PATH_LOGS.'trazabilidades.csv',
            date("d-m-Y H:i:s").';'.';'.
            ';'.
            ';'.
            ';'.
            ';'.
            ';'.
            $i18n[297].$operacion[$idmodulo].$i18n[298].';'.
            "\r\n",
            FILE_APPEND)
            or mostrar_error($i18n[299], true);
    }
}

function muevestock($operacion, $datos, $productoxcomprobante = []) {
    global $modulo;
    global $modulo_singular;
    global $idmodulo;

    $cantidad_original = isset($productoxcomprobante['cantidad_original'])
        ? $productoxcomprobante['cantidad_original']
        : 0;
    $idproductoxcomprobante = isset($productoxcomprobante['idproductox'.$modulo_singular])
        ? $productoxcomprobante['idproductox'.$modulo_singular]
        : 0;

    $resultado_sql = consulta_sql("SELECT productosx".$modulo.".*,
        productos.controlarstock, productos.combo, productos.stocknegativo,
        COALESCE(stock.stockactual, 0) AS stockactual,
        COALESCE(stock.stockminimo, 0) AS stockminimo
        FROM productosx".$modulo."
        INNER JOIN productos ON productosx".$modulo.".idproducto = productos.idproducto
        LEFT JOIN stock ON productos.idproducto = stock.idproducto AND stock.iddeposito = '".$datos['iddeposito']."'
        WHERE ".$idmodulo." = '".$operacion[$idmodulo]."'"
            .($productoxcomprobante ? "AND idproductox".$modulo_singular." = ".$idproductoxcomprobante : "")
        );

    while ($productoxoperacion = array_sql($resultado_sql)) {
        if ($productoxoperacion['controlarstock'] && $productoxoperacion['idproducto']) {
            if ($productoxoperacion['combo'] && !$productoxoperacion['stocknegativo']
                && $productoxoperacion['stockactual'] < $productoxoperacion['cantidad']
                && !$operacion['operacioninversa']
                && !$cantidad_original) {

                $falta = -1 * ($productoxoperacion['stockactual'] - $productoxoperacion['cantidad']);
                mover_stock_combo($productoxoperacion['idproducto'], $falta, $operacion[$idmodulo], $datos['iddeposito']); //faltaba idoperacion
            }
            if ($modulo == 'compras'){
                $productoxoperacion['cantidad'] *= -1; //la lógica es para ventas, si es compras, damos vuelta el valor
                $cantidad_original *= -1;
            }

            consulta_sql("
                INSERT INTO stock SET
                    idproducto = '".$productoxoperacion['idproducto']."',
                    iddeposito = '".$datos['iddeposito']."',
                    stockactual = ".($operacion['operacioninversa'] ? "+" : "-")." '".$productoxoperacion['cantidad']."'
                ON DUPLICATE KEY UPDATE
                    stockactual = stockactual ".($operacion['operacioninversa'] ? "+" : "-")." ".($cantidad_original ? ($productoxoperacion['cantidad'] - $cantidad_original) : $productoxoperacion['cantidad'])."
            ");

            $idproducto = $productoxoperacion['idproducto'];
            $motivo = $modulo_singular;
            $idrelacion = $operacion[$idmodulo];

            //Notificación stock mínimo
            if ($modulo == 'ventas' &&
                $productoxoperacion['controlarstock'] &&
                $productoxoperacion['stockminimo'] > 0 &&
                !$productoxoperacion['stocknegativo']) {
                    notificarStockMinimo($productoxoperacion['idproducto'], $datos['iddeposito']);
            }
            log_productos($idproducto, $motivo, $idrelacion);
        }
    }
}

function muevesaldo($operacion, $datos) {
    global $i18n;
    global $modulo;
    global $modulo_singular;
    global $idmodulo;
    global $id;

    if ($modulo == 'ventas') {
        $operacionxtercero = 'ventasxclientes';
        $idtercero = 'idcliente';
        $operacionpagos = 'ventaspagos';
        $idtipo_operacion = 'idtipoventa';
        $terceropago = 'clientepago';
        $nuevonumero = $datos['numero_ventas_cliente'];
        $totalcaja = $operacion['total'];
        $id_operacionpago = 'idventapago';
        $id_numero_operacionpago = 'idnumeroventapago';
        $buscar_monedas = [
            'clientes' => $operacion['idcliente'],
            'ventas' => $id,
        ];
    } else {
        $operacionxtercero = 'comprasxproveedores';
        $idtercero = 'idproveedor';
        $operacionpagos = 'compraspagos';
        $idtipo_operacion = 'idtipocompra';
        $terceropago = 'proveedorpago';
        $nuevonumero = $datos['numero_compras_proveedor'];
        $totalcaja = -$operacion['total'];
        $id_operacionpago = 'idcomprapago';
        $id_numero_operacionpago = 'idnumerocomprapago';
        $datos['idusuario'] = campo_sql(consulta_sql("SELECT idusuario FROM compras WHERE idcompra = '".$operacion['idcompra']."'"));
        $buscar_monedas = [
            'proveedores' => $operacion['idproveedor'],
            'compras' => $id,
        ];
    }
    if ($datos['idcaja'])
        $buscar_monedas['cajas'] = $datos['idcaja'];

    if ($operacion['operacioninversa'])
        $operacion['total'] = -$operacion['total'];

    $idmonedas = idmonedas($buscar_monedas);
    $idmoneda = $idmonedas[$modulo];
    $total_cotizado = $modulo == 'ventas'
        ? cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $operacion['total'])
        : cotizacion($idmonedas['proveedores'], $idmonedas['compras'], $operacion['total']);

    consulta_sql("INSERT INTO ".$operacionxtercero." (".$idtercero.", ".$idtipo_operacion.", id, fecha, total, numero)
       VALUES ('".$operacion[$idtercero]."', '".$operacion[$idtipo_operacion]."', '".$id."', '".fecha_sql($datos['fecha'])."', '".$total_cotizado."', '".$nuevonumero."')");

    if ($datos['agregarpago'] && $datos['condicionventa'] != 'cuentacorriente' && $datos['condicionventa'] != 'cheque'
        && !$operacion['operacioninversa']
        && $_SESSION['perfil_cajas_ver']
        && $_SESSION['perfil_'.$operacionpagos.'_alta']
        ) {
        consulta_sql("INSERT INTO ".$operacionpagos." (".$idtercero.", idusuario, ".$idmodulo.", idmoneda, fecha, idformapago, total, closed_at)
            VALUES ('".$operacion[$idtercero]."', '".$datos['idusuario']."', '".$operacion[$idmodulo]."', '".$idmoneda."', '".fecha_sql($datos['fecha'])."', '".$datos['idformapago']."', '".$operacion['total']."', NOW())");
        $idoperacionpago = id_sql();

        consulta_sql("UPDATE ".$operacionpagos." SET ".$id_numero_operacionpago." = '".$idoperacionpago."' WHERE ".$id_operacionpago." = '".$idoperacionpago."'");

        consulta_sql("INSERT INTO ".$operacionxtercero." (".$idtercero.", ".$idtipo_operacion.", id, fecha, total, numero)
            VALUES ('".$operacion[$idtercero]."', '0', '".$idoperacionpago."', '".fecha_sql($datos['fecha'])."', '".$total_cotizado."', '".$nuevonumero."')");

        $total_cotizado_cajas = $modulo == 'ventas'
            ? cotizacion($idmonedas['cajas'], $idmonedas['ventas'], $totalcaja)
            : cotizacion($idmonedas['cajas'], $idmonedas['compras'], $totalcaja);

        consulta_sql("INSERT INTO movimientosxcajas (idcaja, idusuario, idconcepto, fecha, total, detalle, tiporelacion, idrelacion)
            VALUES ('".$datos['idcaja']."', '".$datos['idusuario']."', '".$datos['idconcepto']."', '".fecha_sql($datos['fecha'])."', '".$total_cotizado_cajas."', '".$i18n[300].completar_numero($idoperacionpago, 8)."', '".$terceropago."', '".$idoperacionpago."')");

        actualizar_saldo('cajas', $datos['idcaja'], $total_cotizado_cajas);

        consulta_sql("UPDATE controles SET
            ultimoformapago".$modulo_singular." = '".$datos['idformapago']."',
            ultimotipocaja".$modulo_singular." = (SELECT idtipocaja FROM cajas WHERE idcaja = '".$datos['idcaja']."' LIMIT 1),
            ultimoconcepto".$modulo_singular." = '".$datos['idconcepto']."' WHERE idusuario = '".$_SESSION['usuario_idusuario']."' LIMIT 1");
        $_SESSION['control_ultimoformapago'.$modulo_singular] = $datos['idformapago'];
        $_SESSION['control_ultimotipocaja'.$modulo_singular] = campo_sql(consulta_sql("SELECT idtipocaja FROM cajas WHERE idcaja = '".$datos['idcaja']."' LIMIT 1"), 0);
        $_SESSION['control_ultimoconcepto'.$modulo_singular] = $datos['idconcepto'];
    }
}

function recordatorios($datos, $operacion) {
    global $i18n;
    global $modulo;
    global $modulo_singular;
    global $idmodulo;
    global $idtercero;

    if ($modulo == 'ventas') {
        $numeroventa = $operacion['letra'].completar_numero($operacion['puntodeventa'], 5).'-'.completar_numero($operacion['numero'], 8);
        $texto = escape_sql($operacion['nombre'])
            .' <a href=ventas.php?a=ver&amp;id='.$operacion['idventa'].'>'
            .$numeroventa.'</a> | '.$i18n[35]
            .': <a href=clientes.php?a=ver&id='.$operacion['idcliente'].'>'
            .escape_sql($datos['cliente'])."</a>";
    } else {
        $texto = escape_sql($operacion['tipocompra'])
            .' <a href=compras.php?a=ver&amp;id='.$operacion['idcompra'].'>'
            .$datos['numerocompleto'].'</a> | '.$i18n[35]
            .': <a href=proveedores.php?a=ver&id='.$operacion['idproveedor'].'>'
            .escape_sql($datos['proveedor'])."</a>";
    }

    if ($datos['vencimiento1'])
        consulta_sql("INSERT INTO mensajes SET
            idusuario = '".$datos['idusuario']."',
            tipo = 'Recordatorio',
            fecha = '".fecha_sql($datos['vencimiento1'])."',
            texto = '".escape_sql($i18n[85].': '.$texto)."'");
    if ($datos['vencimiento2'])
        consulta_sql("INSERT INTO mensajes SET
            idusuario = '".$datos['idusuario']."',
            tipo = 'Recordatorio',
            fecha = '".fecha_sql($datos['vencimiento2'])."',
            texto = '".escape_sql($i18n[86].': '.$texto)."'");
}

function cerrar_compra($compra, $datos) {
    global $id;

    $datos['estado'] = 'cerrado';
    $datos['closed_at'] = fecha_sql('ahora', true);
    actualizar_operacion($datos);

    if ($datos['muevesaldo']) {
        muevesaldo($compra, $datos);
        $pagos = obtener_pagos('compras', $compra['idcompra']);
        $diferencia = (($compra['operacioninversa'] ? -1 : 1) * $compra['total'] - $pagos);

        $buscar_monedas = [
            'proveedores' => $compra['idproveedor'],
            'compras' => $id,
        ];
        if ($datos['idcaja'])
            $buscar_monedas['cajas'] = $datos['idcaja'];
        if ($compra['tiporelacion'] == 'servicio')
            $buscar_monedas['clientes'] = campo_sql(consulta_sql(
                "SELECT idcliente FROM servicios WHERE idservicio = '{$compra['idrelacion']}'"));
        $idmonedas = idmonedas($buscar_monedas);

        actualizar_saldo('compras', $id, $diferencia);
        actualizar_saldo('proveedores', $compra['idproveedor'], cotizacion($idmonedas['proveedores'], $idmonedas['compras'], $diferencia));

        // La caja sólo se actualiza si se genera el movimiento de pago dentro de la función muevesaldo más arriba

        if ($compra['tiporelacion'] == 'servicio')
            actualizar_saldo('comprasxservicios', $compra['idrelacion'], cotizacion($idmonedas['clientes'], $idmonedas['compras'], $diferencia));
    }

    if ($datos['muevestock'])
        muevestock($compra, $datos);

    if ($datos['recordatorios'])
        recordatorios($datos, $compra);

    if ($_SESSION['perfil_productos_mod'] && $datos['modo_ajuste']) {
        comprobantes_actualizar_precios_productos($id, $datos['modo_ajuste']);

        $resultado_sql_productosxcompras = consulta_sql("SELECT idproducto FROM productosxcompras WHERE idcompra = '$id'");
        while ($productos = array_sql($resultado_sql_productosxcompras)) {
            $idproducto = $productos['idproducto'];
            $motivo = 'compra-costo';
            $idrelacion = $id;
            log_productos($idproducto, $motivo, $idrelacion);
        }
    }
    // trazabilidad($compra);
}

function actualizar_operacion($datos) {
    global $modulo;

    $guardar = array();
    // No hay que modificarlos
    foreach (['estado', 'situacion', 'numero', 'condicionventa', 'muevesaldo', 'muevestock', 'observacion', 'closed_at'] as $campo) {
        if (isset($datos[$campo]))
            $guardar[$campo] = $campo == 'observacion'
                ? escape_sql($datos[$campo])
                : $datos[$campo];
    }

    // Fechas
    $guardar['fecha'] = fecha_sql($datos['fecha'], true);
    $guardar['vencimiento1'] = fecha_sql($datos['vencimiento1'], false);
    $guardar['vencimiento2'] = fecha_sql($datos['vencimiento2'], false);

    if ($modulo == 'ventas') {
        $id = $datos['idventa'];
        $guardar['estadocae'] = in_array($datos['estadocae'], ['pendiente', 'rechazado', 'aprobado']) ? $datos['estadocae'] : 'sin_especificar';
        $guardar['concepto'] = $datos['concepto'];
        $guardar['idusuario'] = $datos['idusuario'];
        $guardar['fechainicio'] = fecha_sql($datos['fechainicio'], false);
        $guardar['fechafin'] = fecha_sql($datos['fechafin'], false);

    } else { // $modulo == 'compras'
        $id = $datos['idcompra'];
        $guardar['numerocompleto'] = $datos['numerocompleto'];
        $guardar['puntodeventa'] = $datos['puntodeventa'];
        $guardar['fechaimputacion'] = $datos['fechaimputacion'];
    }

    $guardar['idmoneda'] = $datos['idmoneda'];
    guardar_sql($modulo, $guardar, $id);

    if ($modulo == 'ventas' && $guardar['estado'] == 'cerrado' && $guardar['estadocae'] == 'rechazado' && afectado_sql()) {
        guardar_sql($modulo, ['estadocae' => 'pendiente'], $id);
        antiafip();
    }
}

function validar_compra($compra, $datos) {
    global $i18n;
    global $modulo;

    if ((!$compra['esfiscal'] && !$datos['numerocompleto'])
        || ($compra['esfiscal'] && (!$datos['numero'] || !intval($datos['numero']) > 0 || !$datos['puntodeventa']))) {
            //El número o el punto de venta son incorrectos
            mensajes_alta($i18n[119]);
            logs(true, '', json_encode($i18n[119]));
            return false;
    } elseif ((!$compra['esfiscal'] && $datos['numerocompleto'] != $compra['numerocompleto']
            && contar_sql(consulta_sql("SELECT idcompra FROM compras WHERE idproveedor = '".$compra['idproveedor']."' AND numerocompleto = '".$datos['numerocompleto']."' LIMIT 1")))
        || ($compra['esfiscal'] && ($datos['numero'] != $compra['numero'] || $datos['puntodeventa'] != $compra['puntodeventa'])
            && contar_sql(consulta_sql("SELECT idcompra FROM compras WHERE idproveedor = '".$compra['idproveedor']."' AND numero = '".$datos['numero']."' AND puntodeventa = '".$datos['puntodeventa']."' LIMIT 1")))
        ) {
            //Ya existe una compra de este proveedor con el mismo tipo de comprobante y el mismo número
            mensajes_alta($i18n[123]);
            logs(true, '', json_encode($i18n[123]));
            return false;
    } elseif (!$datos['fecha'] || $datos['fecha'] == '0000-00-00 00:00') {
        //La fecha de compra no puede estar vacía
        mensajes_alta($i18n[121]);
        logs(true, '', json_encode($i18n[121]));
        return false;
    } else {
        return true;
    }
}

function anular_compra($compra, $datos) {
    if ($compra['muevestock'] && $compra['estado'] == 'cerrado') {
        $productosxcompras = array_all_sql(consulta_sql(
            "SELECT productosxcompras.idproducto, productosxcompras.cantidad, productos.controlarstock, stock.stockactual
            FROM productosxcompras
            LEFT JOIN productos ON productosxcompras.idproducto = productos.idproducto
            LEFT JOIN stock ON productos.idproducto = stock.idproducto AND stock.iddeposito = '".$datos['iddeposito']."'
            WHERE idcompra = '".$compra['idcompra']."'
            "));

        foreach ($productosxcompras as $productoxcompra) {

            if ($compra['operacioninversa'])
                $productoxcompra['cantidad'] = -$productoxcompra['cantidad'];
            if ($productoxcompra['idproducto'] && $productoxcompra['controlarstock'])
                consulta_sql("UPDATE stock
                    SET stockactual = stockactual "
                    .($compra['operacioninversa'] ? "+" : "-")." '".$productoxcompra['cantidad']."'
                    WHERE idproducto = '".$productoxcompra['idproducto']."'
                    AND iddeposito = '".$datos['iddeposito']."' LIMIT 1");

                //log_productos al anular
                $idproducto = $productoxcompra['idproducto'];
                $motivo = 'compra';
                $idrelacion = $compra['idcompra'];
                log_productos($idproducto, $motivo, $idrelacion);
        }
    }

    if ($compra['muevesaldo'] && $compra['estado'] == 'cerrado') {
        $buscar_monedas = [
            'proveedores' => $compra['idproveedor'],
            'compras' => $compra['idcompra'],
        ];
        if ($compra['tiporelacion'] == 'servicio')
            $buscar_monedas['clientes'] = campo_sql(consulta_sql(
                "SELECT idcliente FROM servicios WHERE idservicio = '{$compra['idrelacion']}'"));
        $idmonedas = idmonedas($buscar_monedas);

        consulta_sql("DELETE FROM comprasxproveedores WHERE idtipocompra = '".$compra['idtipocompra']."' AND id = '".$compra['idcompra']."'");

        $diferencia = ($compra['operacioninversa'] ? 1 : -1) * $compra['total'];
        actualizar_saldo('compras', $compra['idcompra'], $diferencia);
        actualizar_saldo('proveedores', $compra['idproveedor'], cotizacion($idmonedas['proveedores'], $idmonedas['compras'], $diferencia));
        if ($compra['tiporelacion'] == 'servicio')
            actualizar_saldo('comprasxservicios', $compra['idrelacion'], cotizacion($idmonedas['clientes'], $idmonedas['compras'], $diferencia));

    }
    if ($compra['esfiscal']) {
        $resultado_sql = consulta_sql("SELECT * FROM categorias_compras WHERE idtipocompra =".$compra['idtipocompra']);
        if ($tipocompra = array_sql($resultado_sql)) {
            $resultado_sql = consulta_sql("SELECT * FROM tablas_comportamientos WHERE nombre like '".$tipocompra["nombre"]." ".$datos['letra']."'");
            if ($comportamiento = array_sql($resultado_sql)) {
                $datos['idcomportamiento'] = $comportamiento["idcomportamiento"];
            }
        }
        $datos['numero_compras_proveedor'] = $datos["letra"].completar_numero($datos['puntodeventa'], 5)."-".completar_numero($datos["numero"], 9);
        $datos['numerocompleto'] = $datos['numero_compras_proveedor'];
    } else {
        $datos['numero_compras_proveedor'] = $datos['numerocompleto'];
        $datos['numero'] = 0;
        $datos['puntodeventa'] = 0;
        $datos['idcomportamiento'] = 0;
    }
    $datos['estado'] = 'anulado';
    return $datos;
}

function aceptar_compra($compra, $datos) {
    $compra['fecha'] = $datos['fecha'];
    $compra['fechaimputacion'] = $datos['fechaimputacion'];
    $datos['muevesaldo'] = $compra['muevesaldo'];
    $datos['muevestock'] = $compra['muevestock'];

    if ($compra['esfiscal'] ) {
        $resultado_sql = consulta_sql("SELECT * FROM categorias_compras WHERE idtipocompra = ".$compra['idtipocompra']);
        if ($tipocompra = array_sql($resultado_sql)) {
            $resultado_sql = consulta_sql("SELECT * FROM tablas_comportamientos WHERE nombre like '".$tipocompra["nombre"]." ".$datos['letra']."'");
            if ($comportamiento = array_sql($resultado_sql)) {
                $datos['idcomportamiento']=$comportamiento["idcomportamiento"];
            }
        }
        $datos['numero_compras_proveedor'] = $datos["letra"].completar_numero($datos['puntodeventa'], 5)."-".completar_numero($datos["numero"], 9);
        $datos['numerocompleto'] = $datos['numero_compras_proveedor'];
    } else {
        $datos['numero_compras_proveedor'] = $datos['numerocompleto'];
        $datos['numero'] = 0;
        $datos['puntodeventa'] = 0;
        $datos['idcomportamiento'] = 0;
    }

    if (($compra['estado'] == 'cerrado' || $compra['estado'] == 'anulado') && $compra['muevesaldo']) {
        // Ya no se puede modificar totales, ni descuentos, ni monedas en el aceptar
        // if ($compra['operacioninversa'])
        //     $compra['total'] = -$compra['total'];
        // total = '".$compra['total']."',
        consulta_sql("UPDATE comprasxproveedores SET
            fecha = '".fecha_sql($datos['fecha'])."',
            numero = '".$datos['numero_compras_proveedor']."'
            WHERE idtipocompra = '".$compra['idtipocompra']."'
            AND id = '".$compra['idcompra']."'");
        consulta_sql("UPDATE comprasxproveedores SET
            numero = '".$datos['numero_compras_proveedor']."'
            WHERE idtipocompra = '0'
            AND numero = '".$compra['numerocompleto']."'");
    }
    return $datos;
}

function anular_venta($venta, $datos) {
    global $i18n;

    if (!$datos['fecha'])
        $datos['fecha'] = date("d-m-Y H:i");
    if (!$datos['numero'])
        $datos['numero'] = $venta['numero'];
    if (($venta['numero'] != $datos['numero']) && contar_sql(consulta_sql("SELECT idventa FROM ventas WHERE idtipoventa = '".$venta['idtipoventa']."' AND numero = '".$datos['numero']."'"))) {
        mensajes_alta($i18n[123]);
        return false;
    } else {
        if ($venta['estado'] == 'cerrado') {
            $datos['muevesaldo'] = $venta['muevesaldo'];
            $datos['muevestock'] = $venta['muevestock'];
            if ($datos['muevestock']) {
                $resultado_sql = consulta_sql("SELECT productosxventas.*, stock.stockactual, productos.controlarstock, productos.combo
                    FROM productosxventas
                    LEFT JOIN productos ON productosxventas.idproducto = productos.idproducto
                    LEFT JOIN stock ON productos.idproducto = stock.idproducto AND stock.iddeposito = '".$datos['iddeposito']."'
                    WHERE idventa = '".$venta['idventa']."'
                    ");
                while ($productoxventa = array_sql($resultado_sql)) {
                    if ($productoxventa['controlarstock']) {
                        consulta_sql("UPDATE stock SET stockactual = stockactual ".($venta['operacioninversa'] ? "-" : "+")." '".$productoxventa['cantidad']."' WHERE idproducto = '".$productoxventa['idproducto']."' AND iddeposito = '".$datos['iddeposito']."' LIMIT 1");
                        //log_productos al anular
                        $idproducto = $productoxventa['idproducto'];
                        $motivo = 'venta-anular';
                        $idrelacion = $venta['idventa'];
                        log_productos($idproducto, $motivo, $idrelacion);
                    }
                }
            }

            if ($datos['muevesaldo']) {
                $buscar_monedas = [
                    'clientes' => $venta['idcliente'],
                    'ventas' => $venta['idventa'],
                ];
                $idmonedas = idmonedas($buscar_monedas);

                consulta_sql("DELETE FROM ventasxclientes WHERE idtipoventa = '".$venta['idtipoventa']."' AND id = '".$venta['idventa']."'");

                $diferencia = ($venta['operacioninversa'] ? 1 : -1) * $venta['total'];
                actualizar_saldo('ventas', $venta['idventa'], cotizacion($idmonedas['ventas'], $idmonedas['clientes'], $diferencia));
                actualizar_saldo('clientes', $venta['idcliente'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));
                if ($venta['tiporelacion'] == 'servicio')
                    actualizar_saldo('ventasxservicios', $venta['idrelacion'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));
            }
        }
        $datos['estado'] = 'anulado';
        $datos['estadocae'] = 'sin_especificar';
    }
    return $datos;
}

function aceptar_ventas($venta, $datos) {
    global $i18n;

    $datos['muevesaldo'] = $venta['muevesaldo'];
    $datos['muevestock'] = $venta['muevestock'];
    $datos['estado'] = $venta['estado'];
    $datos['estadocae'] = $venta['estadocae'];
    if ($venta['estado'] == 'cerrado' || $venta['estado'] == 'anulado') {
        $saldo = obtener_saldo('clientes', $venta['idcliente']);
        if ($venta['total'] <= '0'
            && $venta['discrimina'] != 'R') {
            mensajes_alta($i18n[124]);
            return false;
        } elseif (!$venta['operacioninversa']
            && $datos['muevesaldo']
            && $datos['maxcuentacorriente'] != '0'
            && $saldo + $venta['total'] > $datos['maxcuentacorriente']) {
            mensajes_alta($i18n[117].' ($ '.$venta['total'].') '.$i18n[118].' ($ '.redondeo($saldo).') '.$i18n[119].' $ '.$datos['maxcuentacorriente'].$i18n[120]);
            return false;
        } elseif (($venta['numero'] != $datos['numero']) && contar_sql(consulta_sql("SELECT idventa FROM ventas WHERE idtipoventa = '".$venta['idtipoventa']."' AND numero = '".$datos['numero']."'"))) {
            mensajes_alta($i18n[123]);
            return false;
        }
        //consulta_sql("UPDATE ventas SET situacion='".$datos['situacion']."', numero = '".$datos['numero']."', fecha = '".fecha_sql($datos['fecha'])."', idusuario = '".$datos['idusuario']."', observacion = '".$datos['observacion']."', condicionventa = '".$datos['condicionventa']."', muevesaldo = '".$datos['muevesaldo']."', muevestock = '".$datos['muevestock']."', vencimiento1 = '".fecha_sql($datos['vencimiento1'], false)."', vencimiento2 = '".fecha_sql($datos['vencimiento2'], false)."', concepto = '".$datos['concepto']."', fechainicio = '".fecha_sql($datos['fechainicio'], false)."', fechafin = '".fecha_sql($datos['fechafin'], false)."' WHERE idventa = '".$id."' LIMIT 1");
        if ($datos['muevesaldo']) {
            if ($venta['operacioninversa'])
                $venta['total'] = -$venta['total'];
            $numeroventa = $venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);
            $nuevonumeroventa = $venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($datos['numero'], 8);

            // Ya no se puede modificar totales, ni descuentos, ni monedas en el aceptar
            // $buscar_monedas = [
            //     'clientes' => $venta['idcliente'],
            //     'ventas' => $venta['idventa'],
            // ];
            // $idmonedas = idmonedas($buscar_monedas);

            // consulta_sql("UPDATE ventasxclientes SET fecha = '".fecha_sql($datos['fecha'])."', total = '".cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $venta['total'])."', numero = '".$nuevonumeroventa."' WHERE idtipoventa = '".$venta['idtipoventa']."' AND id = '".$venta['idventa']."'");
            // consulta_sql("UPDATE ventasxclientes SET numero = '".$nuevonumeroventa."' WHERE idtipoventa = '0' AND numero = '".$numeroventa."'");

            consulta_sql("UPDATE ventasxclientes SET fecha = '".fecha_sql($datos['fecha'])."', numero = '".$nuevonumeroventa."' WHERE idtipoventa = '".$venta['idtipoventa']."' AND id = '".$venta['idventa']."'");
            consulta_sql("UPDATE ventasxclientes SET numero = '".$nuevonumeroventa."' WHERE idtipoventa = '0' AND numero = '".$numeroventa."'");
        }
    } else {
        if (!$datos['fecha'])
            $datos['fecha'] = $venta['fecha'];
        if (!$datos['numero'])
            $datos['numero'] = $venta['numero'];
    }
    return $datos;
}
//Fin funciones de refactorización ventas_mod y compras_mod

//Funciones de ajuste masivo
function actualizar_costo($set_costo, $where) {
    consulta_sql("UPDATE productos " . $set_costo . (count($where) ? " WHERE " . implode(" AND ", $where) : ''));
}

function actualizar_precios($set_precios, $where, $idlista, $idproducto = false) {

    completar_tabla_precios($idlista);

    // Si tengo idproducto, y si es combo y no tiene, precio, hago insert
    // No se si sigue haciendo falta ya que agregué completar_tabla_precios, igual el consumo de recursos es poco y no me animo a borrarlo
    if ($idproducto
        && (campo_sql(consulta_sql(
            "SELECT combo FROM productos WHERE idproducto = '".$idproducto."'"))
        && !contar_sql(consulta_sql(
            "SELECT precio FROM precios WHERE idproducto = '".$idproducto."' AND idlista = '".$idlista."'")))) {

        consulta_sql("INSERT INTO precios ". $set_precios .", idproducto = '".$idproducto."', idlista = '".$idlista."'");

    } else {
        consulta_sql("UPDATE precios
            JOIN productos ON precios.idproducto = productos.idproducto "
            . ($idlista ? " AND precios.idlista = '".$idlista."' " : "")
            . $set_precios
            . (count($where) ? " WHERE " . implode(" AND ", $where) : ''));
    }
}

function nombre_numero_ventas($venta)
{
    return $venta['nombre'].' '.$venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);
}

function ajuste_masivo($datos, $calculo, $where) //no me convence llamarlo acá $calculo acá
{
    $datos['idlista'] = $datos['idlistaprecio'];
    if ($datos['solapa'] == 'combo'){
        $wherecosto = $where; //si surge otra idea dinámica de cómo añadir idproducto al array y no morir en el intento, lo cambiamos
        $whereprecio = $where;
        $wherecosto[] = " productos.idproducto = '".$calculo['idcombo']."' ";
        $whereprecio[] = " precios.idproducto = '".$calculo['idcombo']."' ";
        $sucursales = armar_sqls_sucursales();
        puntero_sql($sucursales['listas']);

        $set_costo = "SET costo = ".$calculo['costo']."";
        actualizar_costo($set_costo, $wherecosto);

        while ($lista = array_sql($sucursales['listas'])) {
            if ((!$datos['idlista']) || ($datos['idlista'] && $datos['idlista'] == $lista['idlista'])){
                if ($datos['composicion_modo'] == 'composicion_utilidad') {
                    //Ajustar Costo y Precio según composición. Actualizar utilidad -> default
                    $set_precio = "SET precios.precio = ".$calculo['precio_'.$lista['idlista']]." ";
                    actualizar_precios($set_precio, $whereprecio, $lista['idlista'], $calculo['idcombo']);

                    $set_precio_final = "SET precios.preciofinal = precios.precio * (1 + (SELECT tablas_ivas.valor FROM tablas_ivas WHERE tablas_ivas.idiva = productos.idiva) / 100)";
                    actualizar_precios($set_precio_final, $whereprecio, $lista['idlista'], $calculo['idcombo']);

                    $set_utilidad = "SET precios.utilidad = ((precios.precio / productos.costo - 1) * 100)";
                    actualizar_precios($set_utilidad, $whereprecio, $lista['idlista'], $calculo['idcombo']);

                } elseif ($datos['composicion_modo'] == 'composicion_precio') {
                    //Ajustar costo según composición. Mantener utilidad y actualizar precio
                    $set_precio = "SET precios.precio = (SELECT productos.costo FROM productos WHERE idproducto = '".$calculo['idcombo']."') * (precios.utilidad / 100 + 1)";
                    actualizar_precios($set_precio, $whereprecio, $lista['idlista'], $calculo['idcombo']);

                    $set_precio_final = "SET precios.preciofinal = precios.precio * (1 + (SELECT tablas_ivas.valor FROM tablas_ivas WHERE tablas_ivas.idiva = (SELECT productos.idiva FROM productos WHERE idproducto = '".$calculo['idcombo']."')) / 100)";
                    actualizar_precios($set_precio_final, $whereprecio, $lista['idlista'], $calculo['idcombo']);
                }
            }
        }
        $where = $wherecosto;
    } else if ($datos['solapa'] == 'utilidad') {
        switch ($datos['utilidad_modo']) {
            case 'utilidad_precio':
                $set_utilidad = "SET precios.utilidad = ".$calculo;
                actualizar_precios($set_utilidad, $where, $datos['idlista']);

                $set_precio = "SET precios.precio = ((precios.utilidad / 100) + 1) * productos.costo";
                actualizar_precios($set_precio, $where, $datos['idlista']);

                $set_precio_final = "SET precios.preciofinal = precios.precio * (1 + (SELECT tablas_ivas.valor FROM tablas_ivas WHERE tablas_ivas.idiva = productos.idiva) / 100),
                    productos.updated_at = NOW()";
                actualizar_precios($set_precio_final, $where, $datos['idlista']);
                break;

            case 'utilidad_costo':
                $set_utilidad = "SET precios.utilidad = ".$calculo;
                actualizar_precios($set_utilidad, $where, $datos['idlista']);

                $set_costo = "JOIN precios ON productos.idproducto = precios.idproducto AND precios.idlista = '".$datos['idlista']."'
                    SET productos.costo = (precios.precio / (precios.utilidad / 100 + 1))";
                actualizar_costo($set_costo, $where);
                break;
        }
    } else {
        switch ($datos['modo']) {
            case 'costo_precio':
                $set_costo = "SET costo = (costo ".$calculo.")";
                actualizar_costo($set_costo, $where);

                //Acá no modifico la query porque el insert es únicamente para combos!
                $set_precio = "SET precios.precio = productos.costo * (precios.utilidad / 100 + 1)";
                actualizar_precios($set_precio, $where, $datos['idlista']);

                $set_precio_final = "SET precios.preciofinal = precios.precio * (1 + (SELECT tablas_ivas.valor FROM tablas_ivas WHERE tablas_ivas.idiva = productos.idiva) / 100)";
                actualizar_precios($set_precio_final, $where, $datos['idlista']);
                break;

            case 'costo_utilidad':
                $set_costo = " SET costo = (costo ".$calculo.") ";
                actualizar_costo($set_costo, $where);

                $set_precio = "SET precios.utilidad = ((precios.precio / productos.costo - 1) * 100)";
                actualizar_precios($set_precio, $where, $datos['idlista']);

                $set_precio_final = "SET precios.preciofinal = precios.precio * (1 + (SELECT tablas_ivas.valor FROM tablas_ivas WHERE tablas_ivas.idiva = productos.idiva) / 100)";
                actualizar_precios($set_precio_final, $where, $datos['idlista']);
                break;

            case 'precio_costo':
                $set_precio = "SET precios.precio = (precios.precio " . $calculo . ")";
                actualizar_precios($set_precio, $where, $datos['idlista']);

                $set_costo = "JOIN precios ON productos.idproducto = precios.idproducto AND precios.idlista = '".$datos['idlista']."'
                    SET productos.costo = (precios.precio / (precios.utilidad / 100 + 1))";
                actualizar_costo($set_costo, $where);

                $set_precio_final = "SET precios.preciofinal = precios.precio * (1 + (SELECT tablas_ivas.valor FROM tablas_ivas WHERE tablas_ivas.idiva = productos.idiva) / 100)";
                actualizar_precios($set_precio_final, $where, $datos['idlista']);
                break;

            case 'precio_utilidad':
                $set_precio = "SET precios.precio = (precios.precio " . $calculo . ")";
                actualizar_precios($set_precio, $where, $datos['idlista']);

                $set_utilidad = "SET precios.utilidad = ((precios.precio / productos.costo - 1) * 100)";
                actualizar_precios($set_utilidad, $where, $datos['idlista']);

                $set_precio_final = "SET precios.preciofinal = precios.precio * (1 + (SELECT tablas_ivas.valor FROM tablas_ivas WHERE tablas_ivas.idiva = productos.idiva) / 100),
                    productos.updated_at = NOW()";
                actualizar_precios($set_precio_final, $where, $datos['idlista']);
                break;
        }
    }

    $sql = "SELECT idproducto FROM productos " . (count($where) ? " WHERE " . implode(" AND ", $where) : '');
    $resultado_sql_productosxajuste = consulta_sql($sql);

    while ($productos = array_sql($resultado_sql_productosxajuste)) {
        $idproducto = $productos['idproducto'];
        $motivo = 'ajuste-masivo';
        $idrelacion = '0';
        log_productos($idproducto, $motivo, $idrelacion);
    }
}

function posibles_idcomportamiento_asoc($idcomportamiento)
{
    // La relación no puede ser a cualquier idcomportamiento/código
    switch ($idcomportamiento) {
        // Para 02 y 03 pueden asociarse los tipos de comprobante 01, 02, 03, 04, 05, 34, 39, 60, 63. (no tenemos las superiores a 15)
        case 2:
        case 3:
            $posibles_idcomportamiento_asoc = [1, 2, 3, 4, 5];
            break;
        // Para 07 y 08 pueden asociarse 06, 07, 08, 09, 10, 35, 40, 61 y 64. (no tenemos las superiores a 15)
        case 7:
        case 8:
            $posibles_idcomportamiento_asoc = [6, 7, 8, 9, 10];
            break;
        // Para 12 o 13 pueden asociarse 11, 12, 13 y 15.
        case 12:
        case 13:
            $posibles_idcomportamiento_asoc = [11, 12, 13, 15];
            break;
        // Para 52 o 53 pueden asociarse 51, 52, 53 y 54.
        case 52:
        case 53:
            $posibles_idcomportamiento_asoc = [51, 52, 53, 54];
            break;
        default:
            mostrar_error('Llamado a posibles_idcomportamiento_asoc con idcomportamiento incorrecto: '.$idcomportamiento, true);
    }

    return $posibles_idcomportamiento_asoc;
}

function venta($idventa)
{
    //Para unificar criterios: categorias_clientes.idlista y categorias_ventas.iddeposito quedan como las predeterminadas. Por otro lado, idlista_origen y iddeposito_origen están asociadas a la venta original
    return array_sql(consulta_sql(
        "SELECT ventas.idcliente, ventas.estado, idrelacion, tiporelacion, condicionventa, vencimiento1, vencimiento2, subtotal, ventas.descuento, neto, nogravado, iva, tributos, total, ventas.idlista AS idlista_origen, ventas.iddeposito AS iddeposito_origen, ventas.razonsocial, ventas.situacion, concepto, fechainicio, fechafin, idcomportamiento, ventas.operacioninversa, ventas.idmoneda,
            ventas_ml.ML_order_id, ventas_ml.ML_pack_id, ventas_ml.idtienda,
            cat.auto_aprobar, cat.idtipoventa, cat.iddeposito, categorias_clientes.idlista, cat.tipofacturacion
        FROM ventas
            LEFT JOIN categorias_ventas AS cat ON ventas.idtipoventa = cat.idtipoventa
            LEFT JOIN clientes ON ventas.idcliente = clientes.idcliente
            LEFT JOIN categorias_clientes ON clientes.idtipocliente = categorias_clientes.idtipocliente
            LEFT JOIN ventas_ml ON ventas.idventa = ventas_ml.idventa
        WHERE ventas.idventa = '$idventa'
        LIMIT 1"));
}

function cliente($idcliente)
{
    return array_sql(consulta_sql(
        "SELECT clientes.nombre, clientes.idtipoiva, cuit, tipodoc, dni, clientes.razonsocial, clientes.domicilio,
            tab.nombre AS tipoiva,
            cat.descuento, cat.condicion, cat.idlista, clientes.idlocalidad
        FROM clientes
            LEFT JOIN tablas_condiciones AS tab ON clientes.idtipoiva = tab.idtipoiva
            LEFT JOIN categorias_clientes AS cat ON clientes.idtipocliente = cat.idtipocliente
        WHERE idcliente = '$idcliente'"
        ));
}

function tipoventa($idtipoventa, $boton)
{
    return array_sql(consulta_sql(
        "SELECT idtipoventa, tienesituacion, situacion, ultimonumero, observacion, discrimina, tipofacturacion,
            muevesaldo, muevestock, operacioninversa, idusuario, idcomportamiento, auto_aprobar, iddeposito, deposito_venta_relacionada,
            monedas.idmoneda
        FROM categorias_ventas
            LEFT JOIN monedas ON categorias_ventas.idmoneda = monedas.idmoneda
        WHERE estado = '1'
            AND ".((is_numeric($idtipoventa) && $idtipoventa > 0)
                ? "categorias_ventas.idtipoventa = '$idtipoventa'"
                : "categorias_ventas.nombre = '$boton'"
            )."
        LIMIT 1"));
}

function alta_venta($datos, $venta_relacionada = false)
{
    global $i18n;

    $idcliente = $datos['idcliente'];
    $idventa = $datos['idventa'];
    $idservicio = $datos['idservicio'];
    $proximonumero = $datos['proximonumero'];
    $venta = $datos['venta'];
    $cliente = $datos['cliente'];
    $tipoventa = $datos['tipoventa'];

    if (strlen($cliente['cuit']) == 11)
        $cliente['tipodoc'] = 80;
    else if (!$cliente['dni'])
        $cliente['tipodoc'] = 99;
    else if (!$cliente['tipodoc'] || $cliente['tipodoc'] == 99)
        $cliente['dni'] = $cliente['idtipoiva'] = 0;

    if (!$cliente['tipodoc'])
        $cliente['tipodoc'] = 99;

    if ($venta_relacionada) {
        $idmoneda = $venta['idmoneda'];
        $idlista = $venta['idlista_origen'];
        $iddeposito = $tipoventa['deposito_venta_relacionada'] ? $venta['iddeposito_origen'] : $tipoventa['iddeposito'];

    } else {
        $iddeposito = $tipoventa['iddeposito'];
        $idlista = $cliente['idlista'];
        $idmoneda = campo_sql(consulta_sql(
            "SELECT idmoneda FROM listas WHERE idlista = '".$cliente['idlista']."'"), 'idmoneda');

        if (!$idmoneda)
            $idmoneda = 1;

        if ($tipoventa['idmoneda'] != $idmoneda)
            mensajes_alta($i18n[399]);
    }

    consulta_sql(
        "INSERT INTO ventas SET
                idmoneda = '".$idmoneda."',
                idlista = '".$idlista."',
                iddeposito = '".$iddeposito."',
                estado = 'abierto',
                estadocae = 'sin_especificar',
                idtipoventa = '".$tipoventa['idtipoventa']."',
                situacion = '".($tipoventa['tienesituacion'] ? $tipoventa['situacion'] : 'sin_especificar')."',
                numero = '".$proximonumero."',
                fecha = '".fecha_sql('ahora', true)."',
                idcliente = '".$idcliente."',
                idtipoiva = '".$cliente['idtipoiva']."',
                cuit = '".$cliente['cuit']."',
                tipodoc = '".$cliente['tipodoc']."',
                dni = '".$cliente['dni']."',
                idusuario = '".($tipoventa['idusuario'] == -1 ? $_SESSION['usuario_idusuario'] : $tipoventa['idusuario'])."',
                muevesaldo = '".($_SESSION['sistema_gratis'] ? 0 : $tipoventa['muevesaldo'])."',
                muevestock = '".($_SESSION['sistema_gratis'] ? 0 : $tipoventa['muevestock'])."',
                operacioninversa = '".$tipoventa['operacioninversa']."',
                observacion = '".escape_sql($tipoventa['observacion'])."',
                razonsocial = '".($cliente['cuit'] && !$cliente['razonsocial'] ? escape_sql($cliente['nombre']) : escape_sql($cliente['razonsocial']))."',
                domicilio = '".escape_sql($cliente['domicilio'])."',
                idlocalidad = ".($cliente['idlocalidad'] ? $cliente['idlocalidad'] : "(SELECT idlocalidad FROM configuraciones LIMIT 1)").","
        . ($idservicio
            ? " idrelacion = '".$idservicio."',
                tiporelacion = 'servicio',"
            : ""
            )
        . (!$idventa
            ? " descuento = '".$cliente['descuento']."',
                condicionventa = '".$cliente['condicion']."'"
            : "
                descuento = '".$venta['descuento']."',
                condicionventa = '".$venta['condicionventa']."',
                vencimiento1 = '".$venta['vencimiento1']."',
                vencimiento2 = '".$venta['vencimiento2']."',
                idrelacion = '".$venta['idrelacion']."',
                tiporelacion = '".$venta['tiporelacion']."',
                ML_order_id = '".$venta['ML_order_id']."',"
            . (($venta['tipofacturacion'] == 'electronico'
                && $tipoventa['tipofacturacion'] == 'electronico')
                ? "
                concepto = '".$venta['concepto']."',
                fechainicio = '".$venta['fechainicio']."',
                fechafin = '".$venta['fechafin']."',"
                : "")
            . "
                subtotal = '".$venta['subtotal']."',
                neto = '".$venta['neto']."',
                nogravado = '".$venta['nogravado']."',
                exento = '".$venta['exento']."',
                iva = '".$venta['iva']."',
                tributos = '".$venta['tributos']."',
                total = '".$venta['total']."'"
            ));

    $idventa_nuevo = id_sql();
    insertar_saldo('ventas', $idventa_nuevo);

    if ($venta['ML_order_id']) {
        $resultado_sql = consulta_sql("SELECT * FROM ventas_ml WHERE idventa = '".$idventa."'");

        while ($temp_array = array_sql($resultado_sql)) {
            consulta_sql("INSERT INTO ventas_ml SET
                idventa = '".$idventa_nuevo."',
                ML_order_id = '".$temp_array['ML_order_id']."',
                ML_pack_id = '".$temp_array['ML_pack_id']."',
                ML_shipping_id = '".$temp_array['ML_shipping_id']."',
                idtienda = '".$temp_array['idtienda']."'
            ");
        }
    }

    return $idventa_nuevo;
}

function actualizar_relaciones_venta($idventa, $idventa_nuevo, $venta, $tipoventa)
{
    // Situación automática en venta
    if ($venta['auto_aprobar'] && $venta['situacion'] != 'aprobado' && $venta['idtipoventa'] != $tipoventa['idtipoventa']) {
        consulta_sql("UPDATE ventas SET situacion = 'aprobado' WHERE idventa = '$idventa'");
    }

    // Relación entre ventas
    consulta_sql(
        "INSERT INTO ventasxventas SET
            idventa = '".$idventa."',
            idrelacion = '".$idventa_nuevo."'
        ");

     // Copio los tributos
    $resultado_sql = consulta_sql(
        "SELECT *, categorias_tributos.nombre
        FROM tributosxventas
        INNER JOIN categorias_tributos
        ON tributosxventas.idtributo = categorias_tributos.idtributo
        WHERE idventa ='".$idventa."'");
    while ($datos = array_sql($resultado_sql)) {
         consulta_sql(
        "INSERT INTO tributosxventas SET
                idtributo = '" . $datos['idtributo'] . "',
                baseimponible = '" . $datos['baseimponible'] . "',
                alicuota = '" . $datos['alicuota'] . "',
                importe = '" . $datos['importe'] . "',
                idventa = '" . $idventa_nuevo . "'
        ");
    }

    // Copia de datos extras
    $resultado_sql = consulta_sql(
        "SELECT idextraxmodulo, tipo, nombre FROM extrasxmodulos WHERE modulo = 'ventas'");
    while ($extraxmodulo = array_sql($resultado_sql)) {
        $datoxextra = array_sql(consulta_sql(
            "SELECT * FROM datosxextras
            WHERE idextraxmodulo = {$extraxmodulo['idextraxmodulo']}
                AND idrelacion = $idventa"));
        consulta_sql("INSERT INTO datosxextras (idextraxmodulo, idrelacion, idlistaxextra, texto)
            SELECT '".$extraxmodulo['idextraxmodulo']."', '".$idventa_nuevo."', '".$datoxextra['idlistaxextra']."', '".escape_sql($datoxextra['texto'])."'
            FROM dual
            WHERE NOT EXISTS (
                SELECT 1
                FROM datosxextras
                WHERE idextraxmodulo = '".$extraxmodulo['idextraxmodulo']."'
                AND idrelacion = '".$idventa_nuevo."'
            )
        ");
    }

    // Copio los productos
    $resultado_sql = consulta_sql(
        "SELECT idproducto, codigo, cantidad, idunidad, idiva, nombre, costo AS costo_productosxventas,
            (SELECT productos.costo FROM productos WHERE idproducto = productosxventas.idproducto) AS costo,
            precio, preciofinal, descuento, observacion
        FROM productosxventas
        WHERE idventa = '".$idventa."'
        ORDER BY idproductoxventa");
    if (contar_sql($resultado_sql)) {
        $sql_values = array();
        while ($productoxventa = array_sql($resultado_sql)) {

            // Convierto los No aplica en No gravado si la instancia es Responsable Inscripto
            if ($_SESSION['configuracion_discrimina'] && !$productoxventa['idiva'])
                $productoxventa['idiva'] = 1;

            $sql_values[] = "(
                '".$idventa_nuevo."',
                '".$productoxventa['idproducto']."',
                '".escape_sql($productoxventa['codigo'])."',
                '".$productoxventa['cantidad']."',
                '".$productoxventa['idunidad']."',
                '".$productoxventa['idiva']."',
                '".escape_sql($productoxventa['nombre'])."',
                '".($productoxventa['costo'] ? $productoxventa['costo'] : $productoxventa['costo_productosxventas'])."',
                '".$productoxventa['precio']."',
                '".$productoxventa['preciofinal']."',
                '".$productoxventa['descuento']."',
                '".escape_sql($productoxventa['observacion'])."'
                )";

        }
        consulta_sql("INSERT INTO productosxventas (idventa, idproducto, codigo, cantidad, idunidad, idiva, nombre, costo, precio, preciofinal, descuento, observacion) VALUES " . implode(',', $sql_values));

    }
}

function cerrar_venta($venta, $datos)
{
    global $id;

    $datos['estado'] = 'cerrado';
    $datos['closed_at'] = fecha_sql('ahora', true);
    $datos['estadocae'] = $venta['tipofacturacion'] == 'electronico' ? 'pendiente' : 'sin_especificar';
    $datos['numero_ventas_cliente'] = $venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($datos['numero'], 8);
    actualizar_operacion($datos);

    if ($datos['muevesaldo']) {
        muevesaldo($venta, $datos);
        $pagos = obtener_pagos('ventas', $venta['idventa']);
        $diferencia = (($venta['operacioninversa'] ? -1 : 1) * $venta['total'] - $pagos);

        $buscar_monedas = [
            'clientes' => $venta['idcliente'],
            'ventas' => $id,
        ];
        if ($datos['idcaja'])
            $buscar_monedas['cajas'] = $datos['idcaja'];
        $idmonedas = idmonedas($buscar_monedas);

        actualizar_saldo('ventas', $id, $diferencia);
        actualizar_saldo('clientes', $venta['idcliente'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));

        // La caja sólo se actualiza si se genera el movimiento de pago dentro de la función muevesaldo más arriba

        if ($venta['tiporelacion'] == 'servicio')
            actualizar_saldo('ventasxservicios', $venta['idrelacion'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));
    }

    if ($datos['muevestock']) {
        muevestock($venta, $datos);
    }

    if ($datos['recordatorios']) {
        recordatorios($datos, $venta);
    }

    if ($venta['tipofacturacion'] == 'electronico') {
        fe($venta['idventa']);
    }

    // trazabilidad($venta);
    return true;
}

function cerrar_venta_masiva($idventa)
{
    $_SESSION['omitir_mensajes'] = true;
    $venta = obtener_operacion($idventa);
    $return = false;
    if (validar_cierre($venta, $venta) &&
        ($venta['tipofacturacion'] != 'electronico' || validar_fe($venta['idventa']))) {
        $return = cerrar_venta($venta, $venta);
    }
    $_SESSION['omitir_mensajes'] = false;

    return $return;
}

function modificar_stock_cuenta_corriente($idoperacion, $datos)
{
    global $i18n;

    $operacion = array_sql(consulta_sql("SELECT * FROM " .($GLOBALS['modulo'] == 'ventas' ? 'ventas' : 'compras'). " WHERE " .($GLOBALS['modulo'] == 'ventas' ? 'idventa' : 'idcompra'). " = '$idoperacion'"));

    if ($operacion['estado'] == 'cerrado') {
        if ($operacion['muevestock'] && $datos['cantidad'] != $datos['cantidad_original'] && $datos['idproducto']) {
            muevestock($operacion, $operacion, $datos);
            $codigo = campo_sql(consulta_sql("SELECT codigo FROM productos WHERE idproducto = '".$datos['idproducto']."'"));
            script_flotante('confirmacion', $i18n[369].$codigo, 3000);
        }
    }
}

function validar_deposito_lista_precios($venta, $tipoventa)
{
    global $i18n;
    global $modulo;

    if ($tipoventa['deposito_venta_relacionada']
        && $tipoventa['iddeposito'] != $venta['iddeposito_origen']) {
        mensajes_alta($modulo == 'ventas' ? $i18n[386] : $i18n[339], 'Confirmacion');
    }

    if ($venta['idlista'] != $venta['idlista_origen']) {
        mensajes_alta($modulo == 'ventas' ? $i18n[387] : $i18n[340], 'Confirmacion');
    }
}

function obtenerFormasPagosCompleto() {
    global $i18n_informes;
    $formasdepago = [];
    $formasdepago[] = array(
        'id' => '',
        'valor' => $i18n_informes[199],
        'datasets' => array(
            'data-tipoformapago' => '')
        );
    $formasdepago_sql = consulta_sql("SELECT * FROM tablas_formasdepago WHERE idformapago > 0 ORDER BY nombre");
    while ($formadepago = array_sql($formasdepago_sql)) {
        $formasdepago[] = array(
            'id' => $formadepago['idformapago'],
            'valor' => $formadepago['nombre'],
            'datasets' => array(
                'data-tipoformapago' => $formadepago['tipo'])
            );
    }
    return $formasdepago;
}

function actualizar_tributo_5329($idventa, $idiva) {
    global $i18n_funciones;

    $totales_5329 = array_all_sql(consulta_sql(
        "SELECT COALESCE(SUM(cantidad * precio * (1 - descuento / 100)), 0) AS total, p.idiva
        FROM productosxventas AS pxv
        JOIN productos AS p ON pxv.idproducto = p.idproducto
        WHERE idventa = '$idventa' AND tributo_5329 = 1
        GROUP BY p.idiva"), 'idiva');

    if ($totales_5329[4]['total'] * 0.015 + $totales_5329[5]['total'] * 0.03 >= TOPE_5329) {
        if ($idiva == 4) {
            if ($totales_5329[4]['total'] > 0
                && contar_sql(consulta_sql("SELECT idtributo FROM tributosxventas
                    WHERE idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329')
                        AND idventa = '$idventa' AND alicuota = 1.5"))) {

                consulta_sql("UPDATE tributosxventas SET
                    baseimponible = '{$totales_5329[4]['total']}',
                    importe = baseimponible * 0.015
                    WHERE idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329')
                        AND idventa = '$idventa' AND alicuota = 1.5");

            } else if ($totales_5329[4]['total'] > 0) {

                consulta_sql("INSERT INTO tributosxventas SET
                    idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329'),
                    idventa = '$idventa',
                    baseimponible = '{$totales_5329[4]['total']}',
                    alicuota = 1.5,
                    importe = baseimponible * 0.015");
                script_flotante('informacion', $i18n_funciones[317], 5000);

            } else if ($totales_5329[4]['total'] == 0) {
                consulta_sql("DELETE FROM tributosxventas
                    WHERE idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329')
                        AND idventa = '$idventa' AND alicuota = 1.5");
            }

        } else if ($idiva == 5) {
            if ($totales_5329[5]['total'] > 0
                && contar_sql(consulta_sql("SELECT idtributo FROM tributosxventas
                    WHERE idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329')
                        AND idventa = '$idventa' AND alicuota = 3"))) {

                consulta_sql("UPDATE tributosxventas SET
                    baseimponible = '{$totales_5329[5]['total']}',
                    importe = baseimponible * 0.03
                    WHERE idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329')
                        AND idventa = '$idventa' AND alicuota = 3");

            } else if ($totales_5329[5]['total'] > 0) {

                consulta_sql("INSERT INTO tributosxventas SET
                    idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329'),
                    idventa = '$idventa',
                    baseimponible = '{$totales_5329[5]['total']}',
                    alicuota = 3,
                    importe = baseimponible * 0.03");
                script_flotante('informacion', $i18n_funciones[317], 5000);

            } else if ($totales_5329[5]['total'] == 0) {
                consulta_sql("DELETE FROM tributosxventas
                    WHERE idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329')
                        AND idventa = '$idventa' AND alicuota = 3");
            }
        }

    } else {
        // Dejé de tener tributo 5329 superior al TOPE_5329
        consulta_sql("DELETE FROM tributosxventas
            WHERE idtributo = (SELECT idtributo FROM categorias_tributos WHERE auto = '5329')
                AND idventa = '$idventa'");
    }
}

function obtener_pagos($tiporelacion, $idrelacion, $db = 'link') {
    if ($tiporelacion == 'ventas') {
        $idtiporelacion = 'idventa';
        $operacionpagos = 'ventaspagos';
    } else {
        $idtiporelacion = 'idcompra';
        $operacionpagos = 'compraspagos';
    }
    $pagos = array_sql(consulta_sql("SELECT COALESCE(SUM(".$operacionpagos.".total), 0) AS total
                FROM ".$operacionpagos."
                WHERE ".$operacionpagos.".".$idtiporelacion." = '".$idrelacion."'
                ", $db));

    return ($pagos['total'] ? $pagos['total'] : 0);
}

function obtener_cotizaciones() {
    $monedas = [];
    $resultado_sql = consulta_sql("SELECT * FROM monedas");
    while ($moneda = array_sql($resultado_sql)) {
        $monedas[] = [
            'id' => $moneda['idmoneda'],
            'valor' => $moneda['nombre'],
            'datasets' => [
                'data-simbolo' => $moneda['simbolo'],
                'data-cotizacion' => $moneda['cotizacion']
            ]
        ];
    }
    return $monedas;
}

function crear_factura($id_cliente) {
    //Viene de empresas/empresa_99_script_2.php
    $idusuario = 0;
    $idproducto = 406; //mensual
    $idtipoventas = array(
        'A' => 45, // Factura Electrónica A
        'B' => 48, // Factura Electrónica B
    );
    $idlocalidad = 1; // Villa la Angostura

    $cliente = array_sql(consulta_sql("
        SELECT idcliente, idtipoiva, cuit, dni, tipodoc, razonsocial, domicilio, idlocalidad, nombre, saldo, mail, idmoneda
        FROM clientes
            LEFT JOIN saldos ON saldos.tiporelacion = 'clientes' AND clientes.idcliente = saldos.idrelacion
        WHERE idcliente = $id_cliente", 'admin'));

    $producto = array_sql(consulta_sql("
        SELECT productos.idproducto, nombre, idiva, codigo, costo,
            precios.precio, precios.preciofinal
        FROM productos
            LEFT JOIN precios ON precios.idproducto = productos.idproducto AND idlista = 1
        WHERE productos.idproducto = $idproducto", 'admin'));

    // Veo si el documento es A o B, si es RI o algún Monotributo y tengo CUIT
    $idtipoventa = (in_array($cliente['idtipoiva'], [1, 2, 4, 5]) && $cliente['cuit'] > 0)
        ? $idtipoventas['A']
        : $idtipoventas['B'];

    $fecha = array(
        'año' => (int)date("Y"),
        'mes' => (int)date("n"),
        'factura' => date("Y-m-d 00:00:01"),
        'fechainicio' => date("Y-m-01"),
        'fechafin' => date("Y-m-t"),
        'vencimiento1' => date("Y-m-t"),
    );

    $tiposventas = [];
    $total = $producto['preciofinal'];

    // Si le estoy haciendo una factura B a un RI o Monotributo porque no tiene CUIT, lo cambio a Consumidor Final
    if ($cliente['cuit'] == 0) {
        $cliente['idtipoiva'] = 0;
        $cliente['razonsocial'] = '';
    }

    if (!$tiposventas[$idtipoventa]['idtipoventa']) {
        $tiposventas[$idtipoventa] = array_sql(consulta_sql(
            "SELECT idtipoventa, letra, puntodeventa, ultimonumero, nombre
            FROM categorias_ventas WHERE idtipoventa = '$idtipoventa'", 'admin'));
    }
    $tiposventas[$idtipoventa]['ultimonumero']++;

    consulta_sql(
        "INSERT INTO ventas SET
            idtipoventa = '".$idtipoventa."',
            estado = 'cerrado',
            estadocae = 'pendiente',
            idusuario = '".$idusuario."',
            idcliente = '".$cliente['idcliente']."',
            idtipoiva = '".$cliente['idtipoiva']."',
            numero = '".$tiposventas[$idtipoventa]['ultimonumero']."',
            cuit = '".$cliente['cuit']."',
            dni = '".$cliente['dni']."',
            tipodoc = '".($cliente['cuit'] ? 80 : $cliente['tipodoc'])."',
            razonsocial = '".escape_sql($cliente['razonsocial'])."',
            domicilio = '".escape_sql($cliente['domicilio'])."',
            idlocalidad = ".($cliente['idlocalidad'] ? $cliente['idlocalidad'] : $idlocalidad).",
            condicionventa = 'cuentacorriente',
            concepto = 2,
            fecha = '".$fecha['factura']."',
            fechainicio = '".$fecha['fechainicio']."',
            fechafin = '".$fecha['fechafin']."',
            vencimiento1 = '".$fecha['vencimiento1']."',
            muevesaldo = '1',
            idlista = '1',
            iddeposito = '1',
            observacion = 'Factura Electrónica generada automáticamente'
    ", 'admin');

    $idventa = campo_sql(consulta_sql("SELECT MAX(idventa) FROM ventas WHERE idcliente = '".$cliente['idcliente']."' AND fecha = '".$fecha['factura']."'", 'admin'));

    $buscar_monedas = [
        'clientes' => $cliente['idcliente'],
        'ventas' => $idventa,
    ];
    $idmonedas = idmonedas($buscar_monedas);

    consulta_sql("INSERT INTO productosxventas SET
        idventa = '".$idventa."',
        idproducto = '".$producto['idproducto']."',
        codigo = '".$producto['codigo']."',
        cantidad = '1',
        nombre = '".$producto['nombre']."',
        idiva = '".$producto['idiva']."',
        costo = '".$producto['costo']."',
        precio = '".$producto['precio']."',
        preciofinal = '".$producto['preciofinal']."'
    ", 'admin');

    comprobantes_recalculando($idventa, 'admin');

    consulta_sql("INSERT INTO ventasxclientes SET
        idtipoventa = '".$idtipoventa."',
        idcliente = '".$cliente['idcliente']."',
        id = '".$idventa."',
        fecha = '".$fecha['factura']."',
        total = '".cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $total)."',
        numero = '".numero_comprobante($tiposventas[$idtipoventa]['letra'], $tiposventas[$idtipoventa]['puntodeventa'], $tiposventas[$idtipoventa]['ultimonumero'])."'
    ", 'admin');

    consulta_sql("UPDATE categorias_ventas SET
                    ultimonumero = '".$tiposventas[$idtipoventa]['ultimonumero']."'
                    WHERE idtipoventa = '$idtipoventa'", 'admin');

    actualizar_saldo('ventas', $idventa, cotizacion($idmonedas['ventas'], $idmonedas['clientes'], $total), 'admin');
    actualizar_saldo('clientes', $cliente['idcliente'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $total), 'admin');

    antiafip('admin');
}

function idmoneda($tabla, $idrelacion)
{
    return campo_sql(consulta_sql("SELECT idmoneda FROM $tabla WHERE ".key_tabla($tabla)." = '$idrelacion'"));
}

function idmonedas($tablas)
{
    if (!is_array($tablas))
        mostrar_error('Buscar idmonedas con $tablas no array: '.$tablas, true);

    $implode = [];
    foreach($tablas as $tabla => $idrelacion) {
        if (!$idrelacion)
            mostrar_error('Buscar idmonedas con $idrelacion vacío: '.$tabla, true);
        $implode[] = "(SELECT idmoneda FROM $tabla WHERE ".key_tabla($tabla)." = '$idrelacion') AS $tabla";
    }

    return array_sql(consulta_sql("SELECT ".implode(',', $implode)));
}

function cotizaciones($cotizaciones) {
    $idmonedas = array_unique(array_merge(array_column($cotizaciones, 'idmoneda_origen'), array_column($cotizaciones, 'idmoneda_destino')));
    $cotizaciones_db = array_all_sql(consulta_sql(
        "SELECT idmoneda, cotizacion FROM monedas WHERE idmoneda IN (".implode(',', $idmonedas).")"), 'idmoneda');

    $cotizaciones_convertidas = [];
    foreach ($cotizaciones as $cotizacion) {
        if ($cotizacion['idmoneda_destino'] == $cotizacion['idmoneda_origen'])
            $cotizaciones_convertidas[$cotizacion['id']] = $cotizacion['valor'];
        else if ($cotizaciones_db[$cotizacion['idmoneda_destino']] && $cotizaciones_db[$cotizacion['idmoneda_origen']])
            $cotizaciones_convertidas[$cotizacion['id']] = $cotizacion['valor'] / $cotizaciones_db[$cotizacion['idmoneda_destino']]['cotizacion'] * $cotizaciones_db[$cotizacion['idmoneda_origen']]['cotizacion'];
        else
            mostrar_error('No se encontró cotización en la base de datos para '.$cotizacion['idmoneda_origen'].' o '.$cotizacion['idmoneda_destino']);
    }
    return $cotizaciones_convertidas;
}

function cotizacion($idmoneda_destino, $idmoneda_origen, $valor) {

    if (!$idmoneda_origen) {
        mostrar_error('Llamado a cotizacion sin idmoneda_origen: '.$idmoneda_origen, true);
        return $valor;
    }

    if (!$idmoneda_destino) {
        mostrar_error('Llamado a cotizacion sin idmoneda_destino'.$idmoneda_destino, true);
        return $valor;
    }

    if ($idmoneda_destino == $idmoneda_origen)
        return $valor;

    $monedas = array_all_sql(consulta_sql(
        "SELECT idmoneda, cotizacion, nombre, simbolo
        FROM monedas
        WHERE idmoneda IN ($idmoneda_destino, $idmoneda_origen) ORDER BY idmoneda ASC"),
        'idmoneda');

    $cotizacion =  redondeo($valor / $monedas[$idmoneda_destino]['cotizacion'] * $monedas[$idmoneda_origen]['cotizacion']);

    return $cotizacion;
}

function cotizacion_con_valores($cotizacion_destino, $cotizacion_origen, $valor) {

    if (!$cotizacion_origen) {
        mostrar_error('Llamado a cotizacion_con_valores sin cotizacion_origen: '.$cotizacion_origen, true);
        return $valor;
    }

    if (!$cotizacion_destino) {
        mostrar_error('Llamado a cotizacion_con_valores sin cotizacion_destino: '.$cotizacion_destino, true);
        return $valor;
    }

    if ($cotizacion_destino == $cotizacion_origen)
        return $valor;

    return redondeo($valor / $cotizacion_destino * $cotizacion_origen);
}

function es_cotizacion_anterior($modulo, $id) {

    switch ($modulo) {
        default:
            return true;

        case 'ventas':
            return contar_sql(consulta_sql(
                "SELECT idventa
                FROM ventas
                    JOIN clientes ON ventas.idcliente = clientes.idcliente
                WHERE idventa = '$id' AND muevesaldo = 1
                    AND ventas.idmoneda != clientes.idmoneda
                    AND ventas.closed_at > '0000-00-00 00:00:00'
                    AND (
                        (ventas.idmoneda != 1 AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = ventas.idmoneda AND fechayhora > ventas.closed_at))
                        OR (clientes.idmoneda != 1 AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = clientes.idmoneda AND fechayhora > ventas.closed_at))
                    )"));

        case 'ventaspagos':
            return contar_sql(consulta_sql(
                "SELECT *
                FROM ventaspagos
                    JOIN clientes ON ventaspagos.idcliente = clientes.idcliente
                    LEFT JOIN ventas ON ventaspagos.idventa = ventas.idventa
                    LEFT JOIN movimientosxcajas ON (ventaspagos.idventapago = movimientosxcajas.idrelacion AND movimientosxcajas.tiporelacion = 'clientepago')
                    LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
                WHERE idventapago = '$id'
                    AND ventaspagos.closed_at > '0000-00-00 00:00:00'
                    AND (
                        (ventaspagos.idmoneda != clientes.idmoneda AND clientes.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = clientes.idmoneda AND fechayhora > ventaspagos.fecha))
                        OR (ventaspagos.idmoneda != clientes.idmoneda AND ventaspagos.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = ventaspagos.idmoneda AND fechayhora > ventaspagos.fecha))
                        OR (ventas.idmoneda IS NOT NULL AND ventaspagos.idmoneda != ventas.idmoneda AND ventas.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = ventas.idmoneda AND fechayhora > ventaspagos.fecha))
                        OR (ventas.idmoneda IS NOT NULL AND ventaspagos.idmoneda != ventas.idmoneda AND ventaspagos.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = ventaspagos.idmoneda AND fechayhora > ventaspagos.fecha))
                        OR (cajas.idcaja IS NOT NULL AND ventaspagos.idmoneda != cajas.idmoneda AND cajas.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = cajas.idmoneda AND fechayhora > ventaspagos.fecha))
                        OR (cajas.idcaja IS NOT NULL AND ventaspagos.idmoneda != cajas.idmoneda AND ventaspagos.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = ventaspagos.idmoneda AND fechayhora > ventaspagos.fecha))
                    )"));

        case 'compras':
            return contar_sql(consulta_sql(
                "SELECT idcompra
                FROM compras
                    JOIN proveedores ON compras.idproveedor = proveedores.idproveedor
                WHERE idcompra = '$id' AND muevesaldo = 1
                    AND compras.idmoneda != proveedores.idmoneda
                    AND compras.closed_at > '0000-00-00 00:00:00'
                    AND (
                        (compras.idmoneda != 1 AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = compras.idmoneda AND fechayhora > compras.closed_at))
                        OR (proveedores.idmoneda != 1 AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = proveedores.idmoneda AND fechayhora > compras.closed_at))
                    )"));

        case 'compraspagos':
            return contar_sql(consulta_sql(
                "SELECT *
                FROM compraspagos
                    JOIN proveedores ON compraspagos.idproveedor = proveedores.idproveedor
                    LEFT JOIN compras ON compraspagos.idcompra = compras.idcompra
                    LEFT JOIN movimientosxcajas ON (compraspagos.idcomprapago = movimientosxcajas.idrelacion AND movimientosxcajas.tiporelacion = 'proveedorpago')
                    LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
                WHERE idcomprapago = '$id'
                    AND compraspagos.closed_at > '0000-00-00 00:00:00'
                    AND (
                        (compraspagos.idmoneda != proveedores.idmoneda AND proveedores.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = proveedores.idmoneda AND fechayhora > compraspagos.closed_at))
                        OR (compraspagos.idmoneda != proveedores.idmoneda AND compraspagos.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = compraspagos.idmoneda AND fechayhora > compraspagos.closed_at))
                        OR (compras.idmoneda IS NOT NULL AND compraspagos.idmoneda != compras.idmoneda AND compras.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = compras.idmoneda AND fechayhora > compraspagos.closed_at))
                        OR (compras.idmoneda IS NOT NULL AND compraspagos.idmoneda != compras.idmoneda AND compraspagos.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = compraspagos.idmoneda AND fechayhora > compraspagos.closed_at))
                        OR (cajas.idcaja IS NOT NULL AND compraspagos.idmoneda != cajas.idmoneda AND cajas.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = cajas.idmoneda AND fechayhora > compraspagos.closed_at))
                        OR (cajas.idcaja IS NOT NULL AND compraspagos.idmoneda != cajas.idmoneda AND compraspagos.idmoneda != 1
                            AND EXISTS (SELECT idcotizacion FROM cotizaciones WHERE idmoneda = compraspagos.idmoneda AND fechayhora > compraspagos.closed_at))
                    )"));
    }
}

function notificarStockMinimo($idproducto, $iddeposito) {
    $producto = array_sql(consulta_sql("SELECT nombre, codigo,
                        stockactual, stockminimo
                        FROM productos
                        INNER JOIN stock ON productos.idproducto = stock.idproducto AND stock.iddeposito = '$iddeposito'
                        WHERE productos.idproducto = '$idproducto'"));

    if ($producto['stockactual'] < $producto['stockminimo']) {
        $mensaje = 'El stock actual del producto '.$producto['nombre'].' con Código <a href=productos.php?a=ver&id='.$idproducto.'>'.$producto['codigo'].'</a>'.' ha quedado por debajo del stock mínimo';
        mensajes_alta($mensaje, 'Notificacion', true, 'admin');
    }
}

function moneda_a_texto($total) {
    $formatterES = numfmt_create('es', NumberFormatter::SPELLOUT);
    $tmp_pesos = explode('.', $total);
    if ($tmp_pesos['1'] > 0) {
        return $formatterES->format($tmp_pesos['0']) . ' con ' .  $formatterES->format($tmp_pesos['1']);
    } else {
        return $formatterES->format($tmp_pesos['0']);
    }
}