<?php
function vencimiento_crt($crt)
{
    $array = openssl_x509_parse(file_get_contents($crt));
    if ($array !== false){
        if (!isset($array["validTo_time_t"])){
            return false;
        }
        return $array["validTo_time_t"];
    }
    else{
        return false;
    }
}

function fe($idventa)
{
    switch(como_fe($_SESSION['empresa_idempresa'])) {
        default:
        case 'afipsdk':
            include_once PATH_TOOLS.'afip.php/src/Afip.php';
        case 'pyafipws':
            $_SESSION['path_pyafipws'] = PATH_TOOLS.'pyafipws/';
            wsfe($idventa);
            break;
        case 'pyafipws_2025':
            $_SESSION['path_pyafipws'] = PATH_TOOLS.'pyafipws2025/';
            wsfe($idventa);
            break;
        case 'afipsdk_lambda':
            afipsdk_lambda($_SESSION['empresa_idempresa'], $idventa);
            break;
    }
}

function antiafip($admin = false)
{
    file_put_contents(PATH_LOGS . 'sincae.csv',
    ($admin ? SAAS_ID : $_SESSION['empresa_idempresa']) . "\r\n"
    , FILE_APPEND);
}

function bloquear_wsfe($cuit, $antiafip = false)
{
    if (!existe_wsfe($cuit, 'crt')
        || !existe_wsfe($cuit, 'ini')
        || !existe_wsfe($cuit, 'key')) {
        if (!$antiafip)
            mensajes_alta('Hay un problema con la configuración de su certificado, pongase en contacto con nuestro soporte técnico.');
        return false;
    }

    if (file_exists(PATH_WSFE.$cuit.'.consultando')) {
        if (!$antiafip)
            mensajes_alta('Hay otro usuario cerrando una factura electrónica en este momento, por favor espere unos segundos y vuelva a intentarlo');
        return false;
    }

    if (file_exists(PATH_WSFE.$cuit.'.entrada') || file_exists(PATH_WSFE.$cuit.'.salida'))
        mostrar_error("El archivo $cuit.entrada o $cuit.salida no fue eliminado.", true);

    // Este archivo es para que no se utilice el mismo certificado al mismo tiempo desde 2 usuarios diferentes
    touch(PATH_WSFE.$cuit.'.consultando');
    unlink(PATH_WSFE.$cuit.'.entrada');
    unlink(PATH_WSFE.$cuit.'.salida');

    return true;
}

function desbloquear_wsfe($guardar_salida = false)
{
    // Libero los archivos para que puedan seguir emitiendo
    if (!$guardar_salida) {
        unlink(PATH_WSFE.$_SESSION['configuracion_cuit'].'.entrada');
        unlink(PATH_WSFE.$_SESSION['configuracion_cuit'].'.salida');
    }
    unlink(PATH_WSFE.$_SESSION['configuracion_cuit'].'.consultando');
}

function completar_cuit($tipo_doc = '96', $cuit, $dni, $atras = false)
{
    if ($tipo_doc == '80' && mb_strlen($cuit) == 11) {
        $nro_doc = $cuit;

    } else if (mb_strlen($dni) >= 5) {
        $nro_doc = $atras
            ? $dni.ceros(11 - mb_strlen($dni))
            : ceros(11 - mb_strlen($dni)).$dni;

    } else if (!$dni && $atras) {
        $tipo_doc = '99';
        $nro_doc = '11111111111'; // Parche para que apruebe las NC sin DNI por el momento

    } else if (!$dni || $tipo_doc == '99') {
        $tipo_doc = '99';
        $nro_doc = ceros(11);

    } else {
        $nro_doc = ceros(11 - mb_strlen($dni)).$dni;
    }

    return [$tipo_doc, $nro_doc];
}

function generar_entrada_wsfe($venta)
{
    $id = $venta['idventa'];
    list($fecha, $hora) = explode(' ', $venta['fecha']);
    list($venta['tipo_doc'], $venta['cuit']) = completar_cuit($venta['tipodoc'], $venta['cuit'], $venta['dni']);

    $entrada = '';
    // Encabezado: 0
    // Campo: tipo_reg Posición: 1 Longitud: 1 Tipo: Numerico Decimales:
    $entrada.= '0';
    // Campo: fecha_cbte Posición: 2 Longitud: 8 Tipo: Alfanumerico Decimales:
    $entrada.= completar_numero(str_replace('-', '', $fecha), 8);
    // Campo: tipo_cbte Posición: 10 Longitud: 2 Tipo: Numerico Decimales:
    $entrada.= completar_numero($venta['idcomportamiento'], 2);
    // Campo: punto_vta Posición: 12 Longitud: 4 Tipo: Numerico Decimales:
    $entrada.= completar_numero($venta['puntodeventa'], 4);
    // Campo: cbt_desde Posición: 16 Longitud: 8 Tipo: Numerico Decimales:
    $entrada.= completar_numero($venta['numero'], 8);
    // Campo: cbt_hasta Posición: 24 Longitud: 8 Tipo: Numerico Decimales:
    $entrada.= completar_numero($venta['numero'], 8);
    // Campo: concepto Posición: 32 Longitud: 1 Tipo: Numerico Decimales:
    $entrada.= $venta['concepto'];
    // Campo: tipo_doc Posición: 33 Longitud: 2 Tipo: Numerico Decimales:
    $entrada.= $venta['tipo_doc'];
    // Campo: nro_doc Posición: 35 Longitud: 11 Tipo: Numerico Decimales:
    $entrada.= $venta['cuit'];
    // Campo: imp_total Posición: 46 Longitud: 15 Tipo: Importe Decimales: 3
    $entrada.= completar_numero(number_format($venta['total'], 2, '', ''), 15);
    // Campo: no_usar Posición: 61 Longitud: 15 Tipo: Importe Decimales: 3
    $entrada.= vacios(15);
    // Campo: imp_tot_conc Posición: 76 Longitud: 15 Tipo: Importe Decimales: 3
    $entrada.= completar_numero(number_format($venta['nogravado'], 2, '', ''), 15);
    // Campo: imp_neto Posición: 91 Longitud: 15 Tipo: Importe Decimales: 3
    $entrada.= completar_numero(number_format($venta['neto'], 2, '', ''), 15);
    // Campo: imp_iva Posición: 106 Longitud: 15 Tipo: Importe Decimales: 3
    $entrada.= completar_numero(number_format($venta['iva'], 2, '', ''), 15);
    // Campo: imp_trib Posición: 121 Longitud: 15 Tipo: Importe Decimales: 3
    $entrada.= completar_numero(number_format($venta['tributos'], 2, '', ''), 15);
    // Campo: imp_op_ex Posición: 136 Longitud: 15 Tipo: Importe Decimales: 3
    $entrada.= completar_numero(number_format($venta['exento'], 2, '', ''), 15);
    // Campo: moneda_id Posición: 151 Longitud: 3 Tipo: Alfanumerico Decimales:
    $entrada.= $venta['codigo'];
    // Campo: moneda_ctz Posición: 154 Longitud: 10 Tipo: Importe Decimales: 6
    $entrada.= completar_numero(number_format($venta['cotizacion'], 6, '', ''), 10);
    // Campo: fecha_venc_pago Posición: 164 Longitud: 8 Tipo: Alfanumerico Decimales:
    if ($venta['concepto'] > 1)
        $entrada.= completar_numero(str_replace('-', '', $venta['vencimiento1']), 8);
    else
        $entrada.= vacios(8);
    // Campo: cae Posición: 172 Longitud: 14 Tipo: Numerico Decimales:
    $entrada.= vacios(14);
    // Campo: fch_venc_cae Posición: 186 Longitud: 8 Tipo: Alfanumerico Decimales:
    $entrada.= vacios(8);
    // Campo: resultado Posición: 194 Longitud: 1 Tipo: Alfanumerico Decimales:
    $entrada.= vacios(1);
    // Campo: motivos_obs Posición: 195 Longitud: 1000 Tipo: Alfanumerico Decimales:
    $entrada.= vacios(1000);
    // Campo: err_code Posición: 1195 Longitud: 6 Tipo: Alfanumerico Decimales:
    $entrada.= vacios(6);
    // Campo: err_msg Posición: 1201 Longitud: 1000 Tipo: Alfanumerico Decimales:
    $entrada.= vacios(1000);
    // Campo: reproceso Posición: 2201 Longitud: 1 Tipo: Alfanumerico Decimales:
    $entrada.= '1';
    // Campo: emision_tipo Posición: 2202 Longitud: 4 Tipo: Alfanumerico Decimales:
    $entrada.= vacios(4);
    // Campo: fecha_serv_desde Posición: 2206 Longitud: 8 Tipo: Alfanumerico Decimales:
    if ($venta['concepto'] > 1)
        $entrada.= completar_numero(str_replace('-', '', $venta['fechainicio']), 8);
    else
        $entrada.= vacios(8);
    // Campo: fecha_serv_hasta Posición: 2214 Longitud: 8 Tipo: Alfanumerico Decimales:
    if ($venta['concepto'] > 1)
        $entrada.= completar_numero(str_replace('-', '', $venta['fechafin']), 8);
    else
        $entrada.= vacios(8);

    // Se agregaron campos repetidos con más longitud
    // Campo: tipo_cbte Posición: 2222 Longitud: 3 Tipo: Numerico Decimales:
    $entrada.= completar_numero($venta['idcomportamiento'], 3);
    // Campo: punto_vta Posición: 2225 Longitud: 5 Tipo: Numerico Decimales:
    $entrada.= completar_numero($venta['puntodeventa'], 5);

    // Tributo: 1
    $tributos_sql = consulta_sql(
        "SELECT t.*, c.*
        FROM tributosxventas AS t
            LEFT JOIN categorias_tributos AS c ON t.idtributo = c.idtributo
        WHERE idventa = $id");

    while ($tributo = array_sql($tributos_sql)) {
        // Campo: tipo_reg Posición: 1 Longitud: 1 Tipo: Numerico Decimales:
        $entrada.= "\r\n1";

        // Campo: id Posición: 2 Longitud: 16 Tipo: Alfanumerico Decimales:
        $entrada.= completar_numero($tributo['idtipotributo'], 16);
        // Campo: desc Posición: 18 Longitud: 100 Tipo: Alfanumerico Decimales:
        $entrada.= completar_texto(sanear_string($tributo['nombre']), 100);
        // Campo: base_imp Posición: 118 Longitud: 15 Tipo: Importe Decimales: 2
        $entrada.= completar_numero(number_format($tributo['baseimponible'], 2, '', ''), 15);
        // Campo: alic Posición: 133 Longitud: 15 Tipo: Importe Decimales: 2
        $entrada.= completar_numero(number_format($tributo['alicuota'], 2, '', ''), 15);
        // Campo: importe Posición: 148 Longitud: 15 Tipo: Importe Decimales: 2
        $entrada.= completar_numero(number_format($tributo['importe'], 2, '', ''), 15);
    }

    // Iva: 2
    $bases_imponibles = comprobantes_bases_imponibles($id, $venta['discrimina']);
    // Tributo: 1
    $ivasxventas_sql = consulta_sql(
        "SELECT ivasxventas.idiva, ivasxventas.iva, tablas_ivas.campo
        FROM ivasxventas
            LEFT JOIN tablas_ivas ON ivasxventas.idiva = tablas_ivas.idiva
        WHERE idventa = $id
            AND campo LIKE 'iva_%'");

    while ($ivaxventa = array_sql($ivasxventas_sql)) {

        $bases_imponibles[$ivaxventa['campo']]['total'] = redondeo($bases_imponibles[$ivaxventa['campo']]['total'] * (1 - $venta['descuento'] / 100));

        if ($venta['discrimina'] == 'B') {
            $bases_imponibles[$ivaxventa['campo']]['total']-= $ivaxventa['iva'];
        }

        // Campo: tipo_reg Posición: 1 Longitud: 1 Tipo: Numerico Decimales:
        $entrada.= "\r\n2";
        // Campo: id Posición: 2 Longitud: 16 Tipo: Alfanumerico Decimales:
        $entrada.= completar_numero($ivaxventa['idiva'], 16);
        // Campo: base_imp Posición: 18 Longitud: 15 Tipo: Importe Decimales: 3
        $entrada.= completar_numero(number_format($bases_imponibles[$ivaxventa['campo']]['total'], 2, '', ''), 15);
        // Campo: importe Posición: 33 Longitud: 15 Tipo: Importe Decimales: 3
        $entrada.= completar_numero(number_format($ivaxventa['iva'], 2, '', ''), 15);

    }

    // Comprobante Asociado: 3
    if (in_array($venta['idcomportamiento'], array(2, 3, 7, 8, 12, 13, 52, 53))) {
        $posibles_idcomportamiento_asoc = posibles_idcomportamiento_asoc($venta['idcomportamiento']);
        $comprobante_asociado_sql = consulta_sql(
            "SELECT c.idcomportamiento, c.puntodeventa, v.numero, v.fecha, v.tipodoc, v.cuit, v.dni
            FROM ventas AS v
                INNER JOIN categorias_ventas AS c ON v.idtipoventa = c.idtipoventa
            WHERE idventa IN (SELECT idventa FROM ventasxventas WHERE idrelacion = $id)
                AND v.estado = 'cerrado' AND v.estadocae = 'aprobado'
                AND c.idcomportamiento IN (".implode(',', $posibles_idcomportamiento_asoc).")
                AND c.tipofacturacion = 'electronico'
            LIMIT 1");
        if (contar_sql($comprobante_asociado_sql)) {
            $comprobante_asociado = array_sql($comprobante_asociado_sql);

            list($comprobante_asociado['tipo_doc'], $comprobante_asociado['cuit']) = completar_cuit($comprobante_asociado['tipodoc'], $comprobante_asociado['cuit'], $comprobante_asociado['dni'], true);

            // Sólo me aprobó las notas de crédito cuando tienen CUIT pero pruebo de vuelta pasando todas
            if (true or mb_strlen($comprobante_asociado['cuit']) == 11) {
                // Campo: tipo_reg Posición: 1 Longitud: 1 Tipo: Numerico Decimales:
                $entrada.= "\r\n3";
                // Campo: tipo Posición: 2 Longitud: 3 Tipo: Numerico Decimales:
                $entrada.= completar_numero($comprobante_asociado['idcomportamiento'], 3);
                // Campo: pto_vta Posición: 5 Longitud: 4 Tipo: Numerico Decimales:
                $entrada.= completar_numero($comprobante_asociado['puntodeventa'], 4);
                // Campo: nro Posición: 9 Longitud: 8 Tipo: Numerico Decimales:
                $entrada.= completar_numero($comprobante_asociado['numero'], 8);
                // Campo: fecha Posición: 17 Longitud: 8 Tipo: Numerico
                $entrada.= completar_numero(str_replace('-', '', $comprobante_asociado['fecha']), 8);
                // Campo: cuit Posición: 25 Longitud: 11 Tipo: Numerico
                $entrada.= completar_numero($comprobante_asociado['cuit'], 11);

                // Agregar Período asociado
                // Campo: tipo_reg Posición: 1 Longitud: 1 Tipo: Numerico
                // Campo: fecha_desde Posición: 2 Longitud: 8 Tipo: Numerico
                // Campo: fecha_hasta Posición: 10 Longitud: 8 Tipo: Numerico
            }

        }
    }

    $file = fopen(PATH_WSFE.$_SESSION['configuracion_cuit'].'.entrada', 'w');
    fwrite($file, $entrada);
    fclose($file);

}

function leer_salida_wsfe()
{
    $salida = array();

    if (!is_readable(PATH_WSFE.$_SESSION['configuracion_cuit'].'.salida'))
        // Si veo que todavía no existe el archivo, espero 2 segundos por las dudas
        sleep(2);

    if (!is_readable(PATH_WSFE.$_SESSION['configuracion_cuit'].'.salida')) {
        // Si no puedo leer la respuesta dejo para mandar el error en el switch principal
        $salida['err_code'] = 'sin_salida';
        return $salida;
    }

    $file = fopen(PATH_WSFE.$_SESSION['configuracion_cuit'].'.salida', 'r');
    fgets($file, 172);  //La primera parte es la configurada arriba y no sirve ahora
    // Campo: cae Posición: 172 Longitud: 14 Tipo: Numerico Decimales:
    $salida['cae'] = fgets($file, 15);
    // Campo: fch_venc_cae Posición: 186 Longitud: 8 Tipo: Alfanumerico Decimales:
    $salida['fch_venc_cae'] = fgets($file, 9);
    // Campo: resultado Posición: 194 Longitud: 1 Tipo: Alfanumerico Decimales:
    $salida['resultado'] = fgets($file, 2);
    // Campo: motivos_obs Posición: 195 Longitud: 1000 Tipo: Alfanumerico Decimales:
    $salida['motivos_obs'] = fgets($file, 1000);
    // Campo: err_code Posición: 1195 Longitud: 6 Tipo: Alfanumerico Decimales:
    $salida['err_code'] = fgets($file, 6);
    // Campo: err_msg Posición: 1201 Longitud: 1000 Tipo: Alfanumerico Decimales:
    $salida['err_msg'] = fgets($file, 1000);
    // Campo: reproceso Posición: 2201 Longitud: 1 Tipo: Alfanumerico Decimales:
    $salida['reproceso'] = fgets($file, 1);
    // Campo: emision_tipo Posición: 2202 Longitud: 4 Tipo: Alfanumerico Decimales:
    $salida['emision_tipo'] = fgets($file, 4);
    // Campo: fecha_serv_desde Posición: 2206 Longitud: 8 Tipo: Alfanumerico Decimales:
    $salida['fecha_serv_desde'] = fgets($file, 8);
    // Campo: fecha_serv_hasta Posición: 2214 Longitud: 8 Tipo: Alfanumerico Decimales:
    $salida['fecha_serv_hasta'] = fgets($file, 8);
    fclose($file);

    return $salida;
}

function analizar_salida_wsfe($salida, $venta)
{
    global $id;

    $tipo = ''; // OK se emitió, ARCA cuando se cayó, USER cuando es problema del usuario, SINCAE cuando vamos a cerrar la factura y dejar sin CAE la venta y ERROR cuando hay que revisar
    $mensaje = false;
    $log_error = false;
    $enviar_error = false;
    $afip_caido = false;
    $rechazar = false;

    $salida["motivos_obs"] = str_replace("  ", " ", $salida["motivos_obs"]);
    $salida["err_msg"] = str_replace("  ", " ", $salida["err_msg"]);

    // FACTURA APROBADA CON CAE
    if ($salida['resultado'] == 'A'
        && $salida['cae']
        && checkdate($salida['fch_venc_cae'][4].$salida['fch_venc_cae'][5], $salida['fch_venc_cae'][6].$salida['fch_venc_cae'][7], $salida['fch_venc_cae'][0].$salida['fch_venc_cae'][1].$salida['fch_venc_cae'][2].$salida['fch_venc_cae'][3])) {

        $venta['cae'] = $salida['cae'];
        $venta['vencimientocae'] = $salida['fch_venc_cae'][0].$salida['fch_venc_cae'][1].$salida['fch_venc_cae'][2].$salida['fch_venc_cae'][3].'-'.$salida['fch_venc_cae'][4].$salida['fch_venc_cae'][5].'-'.$salida['fch_venc_cae'][6].$salida['fch_venc_cae'][7];
        $venta['obscae'] = $salida['motivos_obs']
            ? escape_sql(mb_convert_encoding(trim($salida['motivos_obs']), 'UTF-8'))
            : '';

        // Mensajes que no guardo
        if (strpos($venta["obscae"], "El campo Condicion Frente al IVA del receptor resultara obligatorio conforme lo reglamentado"))
            $venta['obscae'] = '';

        consulta_sql("UPDATE ventas SET
            estadocae = 'aprobado',
            cae = '".$venta['cae']."',
            vencimientocae = '".$venta['vencimientocae']."',
            obscae = '".$venta['obscae']."'
            WHERE idventa = '".$venta['idventa']."'");
        guardar_sql('categorias_ventas', ['ultimo_idventa_aprobado' => $venta['idventa']], $venta['idtipoventa']);
        $tipo = 'OK';
        $mensaje = 'Factura APROBADA por ARCA con CAE '.$salida['cae'];
        if ($venta['obscae']
            && !strpos($venta["obscae"], "El credito fiscal discriminado en el presente comprobante solo podra ser computado a efectos del Procedimiento permanente de transicion al Regimen General"))
            mensajes_alta('La factura fue aprobada por ARCA pero con observaciones. Debe revisarla <a href=ventas.php?a=ver&id='.$venta['idventa'].'>aquí</a>', 'Notificacion', true);

        $log_error = true;


    // REVISIÓN DE ERRORES
    } elseif ($salida['err_code'] == 'sin_salida') {

        // Hay algún problema en el certificado que no debería haber llegado hasta acá o ARCA no respondió
        $tipo = 'SINCAE';
        $mensaje = 'Factura cerrada pendiente de CAE. Más información <a href="ayudas.php?a=ver&id=22" target="_blank">aquí</a>';
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "no se encuentra registrado en los padrones")
        || strpos($salida["motivos_obs"], "no se encuentra registrado en los padrones")
        || strpos($salida["err_msg"], "DocNro es invalido")
        || strpos($salida["motivos_obs"], "DocNro es invalido")) {

        // CUIT del cliente incorrecto
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque el CUIT o DNI del cliente NO es correcto. Puede corregirlo <a href="clientes.php?a=mod&id='.$venta['idcliente'].'" target="_blank">aquí</a>';
        $rechazar = 'Factura RECHAZADA por ARCA porque el CUIT o DNI del cliente NO es correcto';
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "se encuentra INACTIVA por haber sido incluida en la consulta de facturas apocrifas")
        || strpos($salida["motivos_obs"], "se encuentra INACTIVA por haber sido incluida en la consulta de facturas apocrifas")) {

        // CUIT del cliente incorrecto
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque el CUIT o DNI del cliente se encuentra INACTIVA por haber sido incluida en la consulta de facturas apocrifas';
        $rechazar = 'Factura RECHAZADA por ARCA porque el CUIT o DNI del cliente se encuentra INACTIVA por haber sido incluida en la consulta de facturas apocrifas';
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "no se encuentra inscripto en condicion ACTIVA")
        || strpos($salida["motivos_obs"], "no se encuentra inscripto en condicion ACTIVA")) {

        // CUIT del cliente incorrecto
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque el cliente NO es Responsable Inscripto. Puede corregirlo <a href="clientes.php?a=mod&id='.$venta['idcliente'].'" target="_blank">aquí</a>';
        $rechazar = 'Factura RECHAZADA por ARCA porque el cliente NO es Responsable Inscripto';
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "No es un comprobante valido bajo el Regimen de la Ley")
        || strpos($salida["motivos_obs"], "No es un comprobante valido bajo el Regimen de la Ley")) {

        // CUIT en RFCE
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque debido al importe facturado, el CUIT receptor se encuentra obligado a recibir Factura de Crédito Electrónica. Para más información visite http://www.afip.gob.ar/facturadecreditoelectronica/ o comuníquese con nuestro soporte técnico.';
        $rechazar = 'Factura RECHAZADA por ARCA porque debido al importe facturado, el CUIT receptor se encuentra obligado a recibir Factura de Crédito Electrónica';
        $log_error = true;

        file_put_contents(PATH_LOGS . 'rfce_mi_pyme.csv',
            date("Y-m-d H:i:s").SEPARADOR_CSV
            .$_SESSION['empresa_idempresa'].SEPARADOR_CSV
            .$id.SEPARADOR_CSV
            .$venta['cuit'].SEPARADOR_CSV
            .$venta['total']."\r\n"
            , FILE_APPEND);

    } elseif (strpos($salida["motivos_obs"], "10016") !== false
        || strpos($salida["motivos_obs"], "Campo CbteFch Debe estar comprendido en el rango")) {

        // La fecha es incorrecta
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque la fecha es incorrecta.<br>Intente con la fecha actual y si el problema persiste escribanos una <a href="ayudas.php" target="_blank">consulta de ayuda</a>';
        $rechazar = 'Factura RECHAZADA por ARCA porque la fecha es incorrecta';
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "El campo FchVtoPago no puede ser anterior a la fecha del comprobante")
        || strpos($salida["motivos_obs"], "El campo FchVtoPago no puede ser anterior a la fecha del comprobante")) {

        // La fecha es incorrecta
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque la fecha del vencimiento no puede ser anterior a la fecha del comprobante.';
        $rechazar = 'Factura RECHAZADA por ARCA porque la fecha del vencimiento no puede ser anterior a la fecha del comprobante.';
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "El campo FchVtoPago es obligatorio si se informa FchServDesde y/o FchServHasta")
        || strpos($salida["motivos_obs"], "El campo FchVtoPago es obligatorio si se informa FchServDesde y/o FchServHasta")) {

        // No tiene fecha de vencimiento
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque la fecha de vencimiento es obligatoria al informar fechas de servicio.';
        $rechazar = 'Factura RECHAZADA por ARCA porque la fecha de vencimiento es obligatoria al informar fechas de servicio.';
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "El campo FchServHasta es obligatorio si se informa FchServDesde y/o FchVtoPago")
        || strpos($salida["motivos_obs"], "El campo FchServHasta es obligatorio si se informa FchServDesde y/o FchVtoPago")
        || strpos($salida["err_msg"], "El campo FchServDesde es obligatorio si se informa FchServHasta y/o FchVtoPago")
        || strpos($salida["motivos_obs"], "El campo FchServDesde es obligatorio si se informa FchServHasta y/o FchVtoPago")) {

        // La fecha es incorrecta
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque la fechas del período desde y hasta son obligatorias al informar fecha de vencimiento.';
        $rechazar = 'Factura RECHAZADA por ARCA porque la fechas del período desde y hasta son obligatorias al informar fecha de vencimiento.';
        $log_error = true;

    } elseif (strpos($salida["motivos_obs"], "Si ImpNeto es mayor a 0 el objeto IVA es obligatorio")
        || strpos($salida["motivos_obs"], "La suma de los campos BaseImp en AlicIva debe ser igual al valor ingresado en ImpNeto")) {

        // El usuario intenta facturar un neto de pocos centavos con IVA
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque el neto gravado no es suficiente para generar un valor de IVA para ser declarado';
        $rechazar = $mensaje;
        $log_error = true;

    } elseif (strpos($salida["motivos_obs"], "10048") !== false
        || strpos($salida["motivos_obs"], "Importe Total")) {

        // El usuario intenta facturar con bases imponibles negativos o importes totales incorrectos
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque tiene bases imponibles negativas o importes totales incorrectos';
        $rechazar = $mensaje;
        $log_error = true;

    } elseif (strpos($salida["motivos_obs"], "10197") !== false
        || strpos($salida["motivos_obs"], "Si el comprobante es Debito o Credito, enviar estructura CbteAsoc o PeriodoAsoc")) {

        // El usuario intenta facturar una nota de crédito/débito mal relacionada
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque falta o es incorrecta la relación con la factura original';
        $rechazar = $mensaje;
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "NO AUTORIZADO A EMITIR COMPROBANTES - LA CUIT INFORMADA NO SE ENCUENTRA AUTORIZADA A EMITIR COMPROBANTES CLASE")) {

        // No autorizado a emitir Factura A
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque todavía NO está autorizado a emitir Factura Electrónica A. Intente emitir una Factura Electrónica B, si puede cerrar correctamente y obtiene el CAE, significa que la configuración del sistema es correcta y debe esperar autorización de ARCA, de lo contrario escribanos una <a href="ayudas.php" target="_blank">consulta de ayuda</a>';
        $rechazar = 'Factura RECHAZADA por ARCA porque todavía NO está autorizado a emitir Factura Electrónica A';
        $log_error = true;

    } elseif (strpos($salida["err_msg"], "NO AUTORIZADO A EMITIR COMPROBANTES - EL PUNTO DE VENTA INFORMADO DEBE ESTAR DADO DE ALTA Y")) {

        // Mal configurado el punto de venta
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque NO tiene correctamente configurado el punto de venta en el sitio de ARCA. Puede ver nuestra ayuda de configuración <a href="https://scripts.saasargentina.com/ayudas_wsfe/principal.html" target="_blank">aquí</a>';
        $rechazar = 'Factura RECHAZADA por ARCA porque NO tiene correctamente configurado el punto de venta en el sitio de ARCA';
        $log_error = true;

    } elseif (in_array($salida['err_code'],
            array('1000', ' 1000', ' 1009', '10015'))
        || strpos($salida["err_msg"], "No aparecio CUIT en lista de relaciones")
        || strpos($salida["err_msg"], "Computador no autorizado a acceder al servicio")
        ) {

        // Error de configuración en ARCA
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA. Debe consultar con su contador el correcto empadronamiento y configuración de sus datos en la página de ARCA. Puede ver nuestra ayuda de configuración <a href="https://scripts.saasargentina.com/ayudas_wsfe/principal.html" target="_blank">aquí</a>. Si el problema persiste escribanos una <a href="ayudas.php" target="_blank">consulta de ayuda</a>';
        $rechazar = 'Factura RECHAZADA por ARCA por un error en el empadronamiento y/o configuración de sus datos en la página de ARCA';
        $log_error = true;

    } elseif (in_array($salida['err_code'],
        array('10061'))) {

        // Error de redondeo que ya no debería suceder
        $tipo = 'ERROR';
        $mensaje = 'Factura RECHAZADA por ARCA por un error en las bases imponibles.<br>Nuestro equipo de soporte técnico ya recibió una copia del error para analizarlo';
        $log_error = true;
        $enviar_error = true;

    } elseif (str_replace(' ', '', $salida['err_code']) == '1001') {

        // Error de fecha o número que ya no debería suceder
        $tipo = 'SINCAE';
        $mensaje = 'Factura cerrada pendiente de CAE. Más información <a href="ayudas.php?a=ver&id=22" target="_blank">aquí</a>';
        $log_error = true;
        $enviar_error = true;

    } elseif (in_array(str_replace(' ', '', $salida['err_code']), array('600', '601', '602'))) {

        // Errores para analizar porque no lo tenemos identificado
        $tipo = 'USER';
        $mensaje = 'Se produjo un error en la comunicación con los servidores de ARCA.<br>Nuestro equipo de soporte técnico ya recibió una copia del error para analizarlo';
        $log_error = true;
        $enviar_error = true;

   } elseif (in_array(str_replace(' ', '', $salida['err_code']), array('500', '501', '502', 'soap', false))
        || strpos($salida["err_msg"], "Error interno de aplicacion")
        || strpos($salida["err_msg"], "Imposible autenticar con WSAA")
        || strpos($salida["err_msg"], "timeout")
        || in_array($salida['err_code'], array(' ', '     ', false))) {

        // Error en los servidores de ARCA
        $tipo = 'SINCAE';
        $mensaje = 'Factura cerrada pendiente de CAE. Más información <a href="ayudas.php?a=ver&id=22" target="_blank">aquí</a>';
        $log_error = true;
        $afip_caido = true;

    } else {

        // Cualquier otro error que no está en identificado
        $tipo = 'ERROR';
        $mensaje = 'Se produjo un error en la comunicación con los servidores de ARCA.<br>Nuestro equipo de soporte técnico ya recibió una copia del error para analizarlo';
        $log_error = true;
        $enviar_error = true;

    }

    return [
        'tipo' => $tipo,
        'mensaje' => $mensaje,
        'log_error' => $log_error,
        'enviar_error' => $enviar_error,
        'afip_caido' => $afip_caido,
        'rechazar' => $rechazar,
    ];

}

function analizar_salida_ultimonumero($salida)
{
    $tipo = 'ULTIMONUMERO';
    $mensaje = false;
    $log_error = false;
    $enviar_error = false;
    $afip_caido = false;
    $rechazar = false;

    if (strpos($salida["err_msg"], 'El punto de venta no se encuentra habilitado a usar en el presente WS')) {

        // Mal configurado el punto de venta
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque NO tiene correctamente configurado el punto de venta en el sitio de ARCA. Puede ver nuestra ayuda de configuración <a href="https://scripts.saasargentina.com/ayudas_wsfe/principal.html" target="_blank">aquí</a>';
        $rechazar = 'Factura RECHAZADA por ARCA porque NO tiene correctamente configurado el punto de venta en el sitio de ARCA';
        $log_error = true;

    } else if (strpos($salida["err_msg"], "key values mismatch")
        || strpos($salida["output"], "key values mismatch")
        || strpos($salida["err_msg"], "Imposible abrir")
        || strpos($salida["output"], "Imposible abrir")
        || strpos($salida["err_msg"], "Archivo de configuracion")
        || strpos($salida["output"], "Archivo de configuracion")) {

        // Mal configurado el certificado
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA porque el Certificado Digital que configuró en el sitio de ARCA es incorrecto. Puede ver nuestra ayuda de configuración <a href="https://scripts.saasargentina.com/ayudas_wsfe/principal.html" target="_blank">aquí</a>. Si el problema persiste escribanos una <a href="ayudas.php" target="_blank">consulta de ayuda</a>';
        $rechazar = 'Factura RECHAZADA por ARCA porque el Certificado Digital que configuró en el sitio de ARCA es incorrecto';
        $log_error = true;

    } else if (strpos($salida["err_msg"], "No aparecio CUIT en lista de relaciones")
        || strpos($salida["output"], "No aparecio CUIT en lista de relaciones")
        || strpos($salida["err_msg"], "Computador no autorizado a acceder al servicio")
        || strpos($salida["output"], "Computador no autorizado a acceder al servicio")) {

        // Mal configurado el punto de venta
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA. Debe consultar con su contador el correcto empadronamiento y configuración de sus datos en la página de ARCA. Puede ver nuestra ayuda de configuración <a href="https://scripts.saasargentina.com/ayudas_wsfe/principal.html" target="_blank">aquí</a>. Si el problema persiste escribanos una <a href="ayudas.php" target="_blank">consulta de ayuda</a>';
        $rechazar = 'Factura RECHAZADA por ARCA por un error en el empadronamiento y/o configuración de sus datos en la página de ARCA';
        $log_error = true;

    } else if (strpos($salida["err_msg"], "11002") !== false
        || strpos($salida["err_msg"], "El punto de venta no se encuentra habilitado")) {

        // Mal configurado el punto de venta
        $tipo = 'USER';
        $mensaje = 'Factura RECHAZADA por ARCA. Debe consultar con su contador ya que el punto de venta no se encuentra habilitado para Webservice. Puede ver nuestra ayuda de configuración <a href="https://scripts.saasargentina.com/ayudas_wsfe/principal.html" target="_blank">aquí</a>. Si el problema persiste escribanos una <a href="ayudas.php" target="_blank">consulta de ayuda</a>';
        $rechazar = 'Factura RECHAZADA por ARCA porque el punto de venta no se encuentra habilitado para Webservice.';
        $log_error = true;

    } else if (strpos($salida["err_msg"], "Tag not found")
        || $salida["output"] == "Consultar ultimo numero:"
        || strpos($salida["output"], "timeout")) {

        // ARCA está caído, no aviso por mail
        $tipo = 'SINCAE';
        $mensaje = 'Factura cerrada pendiente de CAE. Más información <a href="ayudas.php?a=ver&id=22" target="_blank">aquí</a>';
        $log_error = true;

    } else {

        // No se que problema hubo así que envío error para analizarlo
        $tipo = 'ERROR';
        $mensaje = 'Factura cerrada pendiente de CAE. Más información <a href="ayudas.php?a=ver&id=22" target="_blank">aquí</a>';
        $log_error = true;
        $enviar_error = true;

    }

    return [
        'tipo' => $tipo,
        'mensaje' => $mensaje,
        'log_error' => $log_error,
        'enviar_error' => $enviar_error,
        'afip_caido' => $afip_caido,
        'rechazar' => $rechazar,
    ];

}

function rece1_ultimonumero($idtipoventa)
{
    // Hago la consulta
    $tipoventa = array_sql(consulta_sql("SELECT idcomportamiento, puntodeventa FROM categorias_ventas WHERE idtipoventa = '$idtipoventa' LIMIT 1"));
    $output = array();
    exec('python '.$_SESSION['path_pyafipws'].'rece1.py '.PATH_WSFE.$_SESSION['configuracion_cuit'].'.ini /json /ult '.$tipoventa['idcomportamiento'].' '.$tipoventa['puntodeventa'].' 2>&1', $output);
    $archivo = file_get_contents(PATH_WSFE.$_SESSION['configuracion_cuit'].'.salida');
    $json = json_decode($archivo, true)[0];

    // Está todo bien y devuelvo el número
    if (!$json['err_msg'] && is_numeric($json['cbt_desde']))
        return $json['cbt_desde'];

    // Si no se puede autenticar necesito también el output
    if (is_null($json))
        $json = array();
    $json['output'] = $output[0];

    return $json;
}

function afipsdk_ultimonumero($idtipoventa)
{
    try {
        // Obtengo los datos del tipo de venta
        $tipoventa = array_sql(consulta_sql("SELECT idcomportamiento, puntodeventa FROM categorias_ventas WHERE idtipoventa = '$idtipoventa' LIMIT 1"));

        if (!$tipoventa || !$tipoventa['idcomportamiento'] || !$tipoventa['puntodeventa']) {
            return array(
                'err_msg' => 'No se encontraron datos del tipo de venta',
                'output' => 'Error: Tipo de venta no configurado correctamente'
            );
        }

        // Inicializo AFIPSDK
        $afip = new Afip([
            // 'CUIT' => 20409378472,
            'access_token' => AFIPSDK_ACCESS_TOKEN,
            'production' => true,
            'CUIT' => $_SESSION['configuracion_cuit'],
            'cert' => file_get_contents(PATH_WSFE.$_SESSION['configuracion_cuit'].'.crt'),
            'key' => file_get_contents(PATH_WSFE.$_SESSION['configuracion_cuit'].'.key'),
        ]);

        // Obtener último número de comprobante
        $last_voucher = $afip->ElectronicBilling->getLastVoucher(
            $tipoventa['puntodeventa'],
            $tipoventa['idcomportamiento']
        );

        // Si todo está bien, devuelvo el número
        if (is_numeric($last_voucher)) {
            return $last_voucher;
        }

        // Si hay error, devuelvo el formato compatible
        return array(
            'err_msg' => 'Error al obtener último número de comprobante',
            'output' => 'AFIPSDK Error: ' . json_encode($last_voucher)
        );

    } catch (Exception $e) {
        // En caso de excepción, devuelvo el formato compatible
        return array(
            'err_msg' => $e->getMessage(),
            'output' => 'AFIPSDK Exception: ' . $e->getMessage()
        );
    }
}

function validar_fe($id)
{
    $venta = array_sql(
        consulta_sql("SELECT fecha, estadocae, cae, total, concepto, tipodoc, cuit, dni,
                cat.idtipoventa, cat.idcomportamiento, cat.discrimina, cat.tipofacturacion, cat.puntodeventa
            FROM ventas
                LEFT JOIN categorias_ventas AS cat ON ventas.idtipoventa = cat.idtipoventa
            WHERE idventa = '$id'"
        ));


    // Primero verifico de vuelta si corresponde validar_fe
    if ($venta['tipofacturacion'] != 'electronico' || mb_strlen($venta['cae']) == 14 || $venta['estadocae'] == 'aprobado') {
        mostrar_error('Se envió a validar_fe una factura electrónica ya aprobada: '.json_encode($venta), true);
        return true;
    }

    // La fecha sin hora y en formato Y-m-d la uso para algunas comparaciones
    list($fecha, $hora) = explode(' ', $venta['fecha']);

    // Hago varias revisiones antes de llamar a pyafipws
    if ($venta['idcomportamiento'] > 99) {
        mensajes_alta('No se puede realizar una factura electrónica con el comportamiento especificado');
        mostrar_error('No se puede realizar una factura electrónica con el comportamiento especificado', true);
        return false;

    } elseif (mb_strlen($_SESSION['configuracion_cuit']) != 11) {
        mensajes_alta('No puede realizar comunicaciones con ARCA si no tiene configurado su número de CUIT correctamente');
        return false;

    } elseif (!existe_wsfe($_SESSION['configuracion_cuit'], 'crt')
        || !existe_wsfe($_SESSION['configuracion_cuit'], 'ini')
        || !existe_wsfe($_SESSION['configuracion_cuit'], 'key')) {

        mensajes_alta('La configuración de su certificado digital para emitir facturas electrónicas no es correcta, por favor pongase en contacto con el soporte técnico', 'Notificacion', true);
        // mostrar_error('La configuración de su certificado digital para emitir facturas electrónicas no es correcta, por favor pongase en contacto con el soporte técnico', true);
        return false;

    } elseif (vencimiento_crt(PATH_WSFE.$_SESSION['configuracion_cuit'].'.crt') === false) {
        mensajes_alta('Su certificado digital para emitir facturas electrónicas es invalido y fue eliminado. <a href=ayudas.php?a=ver&id=27>Más información</a>', 'Notificacion', true);
        baja_wsfe($_SESSION['configuracion_cuit'], 'crt');
        // mostrar_error('Su certificado digital para emitir facturas electrónicas es invalido: '.$_SESSION['configuracion_cuit'].'.crt', true);
        return false;

    } elseif (vencimiento_crt(PATH_WSFE.$_SESSION['configuracion_cuit'].'.crt') < time()) {
        mensajes_alta('Su certificado digital para emitir facturas electrónicas se encuentra vencido. <a href=ayudas.php?a=ver&id=27>Más información</a>', 'Notificacion', true);
        // mostrar_error('Su certificado digital para emitir facturas electrónicas se encuentra vencido', true);
        return false;

    } elseif (!$venta['puntodeventa']) {
        mensajes_alta('La configuración del punto de venta es incorrecta. Puede corregir la configuración <a href="configuraciones.php?a=modventa&id='.$venta['idtipoventa'].'" target="_blank">aquí</a>');
        return false;

    } elseif ($venta['discrimina'] == 'A' && mb_strlen($venta['cuit']) != 11) {
        mensajes_alta('El cliente debe tener especificado el CUIT antes de realizar una factura A.');
        return false;

    } elseif ($_SESSION['configuracion_cuit'] == $venta['cuit']) {
        mensajes_alta('No se puede realizar una factura electrónica a un cliente con el mismo CUIT que la empresa');
        return false;

    } elseif (($venta['discrimina'] == 'B' || $venta['discrimina'] == 'C')
        AND $venta['total'] >= TOPE_AFIP_DNI
        AND (mb_strlen($venta['cuit']) != 11)
        AND (mb_strlen($venta['dni']) < 5 || $venta['tipodoc'] == '99')
        ) {
        mensajes_alta('El cliente debe tener especificado el número de documento para realizar una factura mayor o igual a $'.TOPE_AFIP_DNI.'.');
        return false;

    } elseif ($venta['total'] >= TOPE_AFIP
        AND en_rfce($venta['cuit'])) {
        mensajes_alta('Este CUIT receptor se encuentra dentro del listado para emitir Factura de Crédito Electrónica y no se puede emitir una Factura Electrónica normal. Para más información visite http://www.afip.gob.ar/facturadecreditoelectronica/ o comuníquese con nuestro soporte técnico.');
        file_put_contents(PATH_LOGS . 'rfce_mi_pyme.csv',
            date("Y-m-d H:i:s").SEPARADOR_CSV
            .$_SESSION['empresa_idempresa'].SEPARADOR_CSV
            .$id.SEPARADOR_CSV
            .$venta['cuit'].SEPARADOR_CSV
            .$venta['total']."\r\n"
            , FILE_APPEND);
        return false;

    } elseif ((date("m", strtotime($venta['fecha'])) > date("m") && date("Y", strtotime($venta['fecha'])) == date("Y"))
            || date("Y", strtotime($venta['fecha'])) > date("Y")
            ) {
        mensajes_alta('La fecha de la factura no puede ser del mes próximo');
        return false;

    } elseif ($venta['vencimiento1']
        && (!$venta['fechafin'] || $venta['fechafin'] == '0000-00-00'
            || !$venta['fechainicio'] || $venta['fechainicio'] == '0000-00-00')) {
        mensajes_alta('La fecha de período facturas desde y hasta son obligatorias si se informa fecha de vencimiento período facturado');
        return false;

    } elseif ( // Si el concepto es 1 puede no ser mayor/menor a 5 días, en cambio si es otro no puede ser 10 días
        ($venta['concepto'] == 1 && (time() - 432000) > strtotime($fecha) || strtotime($fecha) > (time() + 432000))
        || ($venta['concepto'] > 1 && (time() - 864000) > strtotime($fecha) || strtotime($fecha) > (time() + 864000))
            ) {
        mensajes_alta('La fecha de la factura no puede ser menor o mayor a '
            .($venta['concepto'] == 1 ? '5' : '10')
            .' días de la actual para el concepto a incluir seleccionado');
        return false;

    } else if (in_array($venta['idcomportamiento'], [2,3,7,8,12,13,52,53])
        && !contar_sql(consulta_sql("SELECT idventa
            FROM ventas AS v
                INNER JOIN categorias_ventas AS c ON v.idtipoventa = c.idtipoventa
            WHERE idventa IN (SELECT idventa FROM ventasxventas WHERE idrelacion = $id)
                AND v.estado = 'cerrado' AND v.cae != '' AND estadocae = 'aprobado'
                AND c.idcomportamiento IN (1,2,3,6,7,8,11,12,13,51,52,53)
                AND c.tipofacturacion = 'electronico'"))) {
        mensajes_alta('Las notas de crédito/débito electrónicas deben estar relacionadas a una factura electrónica con la misma letra y con CAE aprobado.');
        return false;
    }

    return true;
}

function wsfe($id, $antiafip = false, $guardar_salida = false)
{
    // if (ESTADO == 'desarrollo')
    //     return false;

    $venta = array_sql(
        consulta_sql("SELECT idventa, fecha, estadocae, cae, tipodoc, cuit, dni, numero,
                total, nogravado, neto, iva, tributos, exento, descuento,
                concepto, vencimiento1, fechainicio, fechafin,
                cat.idtipoventa, cat.idcomportamiento, cat.discrimina, cat.tipofacturacion, cat.letra, cat.puntodeventa, cat.ultimo_idventa_aprobado,
                m.codigo, m.cotizacion
            FROM ventas
                LEFT JOIN categorias_ventas AS cat ON ventas.idtipoventa = cat.idtipoventa
                LEFT JOIN monedas AS m ON ventas.idmoneda = m.idmoneda
            WHERE idventa = '$id'"
        ));
    // La fecha sin hora y en formato Y-m-d la uso para algunas comparaciones
    list($fecha, $hora) = explode(' ', $venta['fecha']);

    // Primero verifico si corresponde intentar emitir
    if ($venta['tipofacturacion'] != 'electronico' || mb_strlen($venta['cae']) == 14 || $venta['estadocae'] != 'pendiente') {
        if (!$antiafip)
            mostrar_error('Se envió a wsfe una venta que no corresponde: '.json_encode($venta), true);
        return 'OK';
    }

    // Si hay una factura del mismo tipo esperando CAE, dejo esta en cola
    if (!$antiafip && contar_sql(consulta_sql(
        "SELECT idventa FROM ventas
        WHERE estadocae = 'pendiente'
            AND idtipoventa = '{$venta['idtipoventa']}'
            AND idventa != '$id' LIMIT 1"))) {
        mensajes_alta('Factura en cola de espera de CAE. Más información <a href="ayudas.php?a=ver&id=22" target="_blank">aquí</a>');
        return false;
    }


    // EMPEZAMOS A PROCESAR LA FACTURA
    if (!bloquear_wsfe($_SESSION['configuracion_cuit'], $antiafip))
        return false;


    $return = []; // Para guardar información y usar tipo flag

    // Buscamos el número que dice ARCA
    if (como_fe($_SESSION['empresa_idempresa']) == 'pyafipws' && !count($return)) {
        $ultimonumero = rece1_ultimonumero($venta['idtipoventa']);
        if (!is_numeric($ultimonumero)) {
            $salida = $ultimonumero;
            $return = analizar_salida_ultimonumero($ultimonumero);
        }

    } elseif (!count($return)) { // 
        $ultimonumero = afipsdk_ultimonumero($venta['idtipoventa']);
        if (!is_numeric($ultimonumero)) {
            $salida = $ultimonumero;
            $return = analizar_salida_ultimonumero($ultimonumero);
        }
    }

    // Actualizo numero de venta
    if (!count($return)) {
        $nuevonumero = actualizar_numero_venta($venta, $ultimonumero, $antiafip);
        if ($nuevonumero === true) {
            $return = [
                'tipo' => 'OK',
                'mensaje' => false,
                'log_error' => true,
                'enviar_error' => false,
                'afip_caido' => false,
                'rechazar' => false,
            ];
        } else if (!is_numeric($nuevonumero)) {
            $salida = $nuevonumero;
            $return = [
                'tipo' => 'NUMERO',
                'mensaje' => 'Factura cerrada pendiente de CAE. Más información <a href="ayudas.php?a=ver&id=22" target="_blank">aquí</a>',
                'log_error' => true,
                'enviar_error' => false,
                'afip_caido' => false,
                'rechazar' => false,
            ];
        } else {
            $numero_original = $venta['numero'];
            $venta['numero'] = $nuevonumero;
        }
    }

    // Verifico que la anterior esté ok
    if (!sin_control_numeracion($_SESSION['empresa_idempresa'])
        && !count($return)
        && $venta['numero'] > 1) {
        if ($venta['ultimo_idventa_aprobado'] && !rece1_verificar($venta['ultimo_idventa_aprobado'])) {
            $salida = $nuevonumero;
            $return = [
                'tipo' => 'ANTERIOR',
                'mensaje' => 'Factura cerrada pendiente de CAE. Más información <a href="ayudas.php?a=ver&id=22" target="_blank">aquí</a>',
                'log_error' => true,
                'enviar_error' => true,
                'afip_caido' => false,
                'rechazar' => false,
            ];
        }
    }

    // Verifico que no tenga No Aplica si es RI
    if ($_SESSION['configuracion_discrimina'] && contar_sql(consulta_sql(
        "SELECT idiva FROM productosxventas WHERE idventa = '$id' AND idiva = 0 LIMIT 1"
    ))) {
        consulta_sql("UPDATE productosxventas SET idiva = 1 WHERE idventa = '$id' AND idiva = 0");
        consulta_sql("UPDATE productos SET idiva = 1 WHERE idiva = 0");
    }

    // Esta todo ok, ya puedo intentar obtener CAE
    if (!count($return)) {

        if (!sin_control_numeracion($_SESSION['empresa_idempresa'])) {
            // Verifico que no haya CAEs aprobados posteriores a la fecha del comprobante
            $fecha_ultimo_cae = campo_sql(consulta_sql(
                "SELECT DATE_FORMAT(fecha,'%Y-%m-%d')
                FROM ventas
                WHERE estadocae = 'aprobado'
                ORDER BY fecha DESC LIMIT 1"));

            // La fecha no puede ser anterior a la última aprobada. Si el concepto es 1 no puede ser mayor/menor a 5 días, en cambio si es otro puede ser 10 días
            if (strtotime($fecha) < strtotime($fecha_ultimo_cae)
                || ($venta['concepto'] == 1 && (time() - 432000) > strtotime($fecha) || strtotime($fecha) > (time() + 432000))
                || ($venta['concepto'] > 1 && (time() - 864000) > strtotime($fecha) || strtotime($fecha) > (time() + 864000))
                ) {
                $fecha = $fecha_ultimo_cae;
                $venta['fecha'] = $fecha.' '.$hora;
                guardar_sql('ventas', ['fecha' => $fecha.' '.$hora], $id);
                if (!$antiafip)
                    mensajes_alta("Se actualizó la fecha del comprobante para que sea aprobado por ARCA", 'Confirmacion');
            }
        }

        // GENERAR ARCHIVO DE ENTRADA
        generar_entrada_wsfe($venta);

        // LLAMAR A PYAFIPWS
        $output = array(); // Por el momento no lo usamos
        exec('python '.$_SESSION['path_pyafipws'].'rece1.py '.PATH_WSFE.$_SESSION['configuracion_cuit'].'.ini', $output);

        // LEO EL ARCHIVO DE SALIDA
        $salida = leer_salida_wsfe();
        $return = analizar_salida_wsfe($salida, $venta);

    }

    desbloquear_wsfe($guardar_salida);

    $tipo = $return['tipo'];
    $mensaje = $return['mensaje'];
    $log_error = $return['log_error'];
    $enviar_error = $return['enviar_error'];
    $afip_caido = $return['afip_caido'];
    $rechazar = $return['rechazar'];

    if ($rechazar) {
        guardar_sql('ventas', ['estadocae' => 'rechazado', 'obscae' => $rechazar], $id);
        guardar_sql('categorias_ventas', ['ultimonumero' => $ultimonumero], $venta['idtipoventa']);
        guardar_sql('ventasxclientes', ['numero' => 'RECHAZADA ARCA'],
            ['idtipoventa' => $venta['idtipoventa'], 'id' => $id]);
        mensajes_alta(
            str_replace(
                'Factura RECHAZADA por ARCA',
                '<a href=ventas.php?a=ver&id='.$id.'>Factura RECHAZADA por ARCA</a>',
                $rechazar), 'Notificacion');
    }

    // ACTUALIZO NÚMERO SI CORRESONDE
    if ($tipo == 'OK' && $numero_original != $venta['numero']) {
        guardar_sql('categorias_ventas', ['ultimonumero' => $venta['numero']], $venta['idtipoventa']);
        guardar_sql('ventas', ['numero' => $venta['numero']], $venta['idventa']);
        guardar_sql('ventasxclientes',
            ['numero' => numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])],
            ['idtipoventa' => $venta['idtipoventa'], 'id' => $venta['idventa']]);
    }

    // GENERO AVISOS DE LO QUE PASÓ
    if ($antiafip && $tipo == 'OK') {
        mensajes_alta($mensaje, 'Confirmacion');

    } else if (!$antiafip && $mensaje) {
        mensajes_alta($mensaje, $tipo == 'OK' ? 'Confirmacion' : 'Alerta');
    }

    // Guardo el idempresa que tiene Pendientes de CAE para analizar por el script que los cierra
    if (in_array($tipo, ['SINCAE', 'NUMERO', 'ANTERIOR'])) {
        antiafip();
    }

    // Envio error solamente si estoy analizando a esta empresa
    if ($enviar_error && $antiafip && $GLOBALS['idempresas']) {
        $error = 'ARCA/WSFE - '.$tipo.'<br/><br/>'
            .$mensaje.'<br /><br/>'
            .'salida: '.json_encode($salida).'<br/><br/>'

            .'cuit emisor: '.$_SESSION['configuracion_cuit'].'<br/>'
            .'cuit receptor: '.$venta['cuit'].'<br/>'
            .'fecha: '.$venta['fecha'].'<br/>'
            .'idtipoventa: '.$venta['idtipoventa'].'<br/>'
            .'idcomportamiento: '.$venta['idcomportamiento'].'<br/>'
            .'puntodeventa: '.$venta['puntodeventa'].'<br/>'
            .'numero: '.$venta['numero'].'<br/>'
            .'total: '.$venta['total'].'<br/>'
            .'discrimina: '.$venta['discrimina'];
        mostrar_error($error, true);
    }

    if ($log_error) {
        file_put_contents(PATH_LOGS . 'afip_caido.csv',
            date("Y-m-d H:i:s") . SEPARADOR_CSV
            . $tipo . SEPARADOR_CSV
            . $_SESSION['empresa_idempresa'] . SEPARADOR_CSV
            . $mensaje . SEPARADOR_CSV
            . json_encode($salida) . "\r\n"
            , FILE_APPEND);
    }

    if ($tipo == 'OK' || $tipo == 'SINCAE')
        return $tipo;
    else
        return false;

}

function rece1_get($idcomportamiento, $puntodeventa, $numero)
{
    // Hago la consulta
    $output = array();
    exec('python '.$_SESSION['path_pyafipws'].'rece1.py '.PATH_WSFE.$_SESSION['configuracion_cuit'].'.ini /json /get '.$idcomportamiento.' '.$puntodeventa.' '.$numero.' 2>&1', $output);
    $archivo = file_get_contents(PATH_WSFE.$_SESSION['configuracion_cuit'].'.salida');
    $json = json_decode($archivo, true)[0];

    // No existe el comprobante
    if (strpos($json['err_msg'], 'NoneType'))
        return false;

    // No responde ARCA
    if (strpos($json['err_msg'], 'Connection reset by peer')
        || strpos($json['err_msg'], 'timed out'))
        return false;

    // Está todo bien y devuelvo el json
    if ($json['err_msg']
        || !(is_numeric($json['cae'])
            && mb_strlen($json['cae']) == 14
            && $json['cae'] != '              ')
        || !(is_numeric($json['cbt_desde'])
            && ($json['cbt_desde'] > 0)
            && ($json['cbt_desde'] == $json['cbt_hasta']))
        ) {

        // Envío error solamente si estoy investigando esta empresa con antiafip
        if ($GLOBALS['idempresas']) {
            mostrar_error('Error consultando rece1_get con<br>'
                .'idcomportamiento: '.$idcomportamiento.'<br>'
                .'puntodeventa: '.$puntodeventa.'<br>'
                .'numero: '.$numero.'<br>'
                .'salida: '.$archivo.'<br>'
                .'output: '. json_encode($output), true);
        }

        return false;
    }

    $json['cuit'] = $json['tipo_doc'] == '80' ? $json['nro_doc'] : 0;
    $json['dni'] = $json['tipo_doc'] == '96' ? $json['nro_doc'] : 0;
    $json['tipodoc'] = $json['tipo_doc'];

    $json['numero'] = $json['cbt_desde'];
    $json['total'] = $json['imp_total'];

    $json['fecha'] = $json['fecha_cbte']
        ? $json['fecha_cbte'][0].$json['fecha_cbte'][1].$json['fecha_cbte'][2].$json['fecha_cbte'][3]
            .'-'.$json['fecha_cbte'][4].$json['fecha_cbte'][5]
            .'-'.$json['fecha_cbte'][6].$json['fecha_cbte'][7]
        : '0000-00-00';
    $json['vencimientocae'] = $json['fch_venc_cae']
        ? $json['fch_venc_cae'][0].$json['fch_venc_cae'][1].$json['fch_venc_cae'][2].$json['fch_venc_cae'][3]
            .'-'.$json['fch_venc_cae'][4].$json['fch_venc_cae'][5]
            .'-'.$json['fch_venc_cae'][6].$json['fch_venc_cae'][7]
        : '0000-00-00';

    return $json;
}

function rece1_verificar($idventa, $verificar_cae = true)
{
    $venta = array_sql(
        consulta_sql("SELECT idventa, fecha, estadocae, cae, tipodoc, cuit, dni, numero,
                total, nogravado, neto, iva, tributos, exento, descuento,
                concepto, vencimiento1, fechainicio, fechafin, vencimientocae,
                cat.idcomportamiento, cat.puntodeventa
            FROM ventas
                LEFT JOIN categorias_ventas AS cat ON ventas.idtipoventa = cat.idtipoventa
            WHERE idventa = '$idventa'"
        ));

    if (!$venta['idcomportamiento'] || !$venta['puntodeventa'] || !$venta['numero']) {
        mostrar_error('Error pidiendo rece1_verificar sin datos<br>venta: '.json_encode($venta), true);
        return false;
    }

    // La fecha sin hora y en formato Y-m-d la uso para algunas comparaciones
    list($fecha, $hora) = explode(' ', $venta['fecha']);
    if (como_fe($_SESSION['empresa_idempresa']) == 'pyafipws')
        $rece1_get = rece1_get($venta['idcomportamiento'], $venta['puntodeventa'], $venta['numero']);
    else // como_fe = 'afipsdk'
        $rece1_get = afipsdk_get($venta['idcomportamiento'], $venta['puntodeventa'], $venta['numero']);

    // ARCA está caído
    if ($rece1_get === false)
        return false;

    if (($rece1_get['tipo_doc'] == '80' && $venta['cuit'] != $rece1_get['cuit'])
        || ($rece1_get['tipo_doc'] == '96' && $venta['dni'] != $rece1_get['dni'])

        || $fecha != $rece1_get['fecha']
        || $venta['numero'] != $rece1_get['numero']
        || ($venta['cae'] != $rece1_get['cae'] && $verificar_cae)
        || $venta['total'] != $rece1_get['total']
        ) {

        $mensaje = 'Error de venta con datos diferentes que el rece1_get:<br>'
            .'venta: '.json_encode($venta).'<br>'
            .'rece1_get: '.json_encode($rece1_get).'<br>'
            .'<br>Diferencias<br>';
        if ($venta['dni'] != $rece1_get['dni'] && $venta['cuit'] != $rece1_get['cuit'])
            $mensaje.= 'dni: '.$venta['dni'] .' != '. $rece1_get['dni'].'<br>'
                .'cuit: '.$venta['cuit'] .' != '. $rece1_get['cuit'].'<br>';
        if ($fecha != $rece1_get['fecha'])
            $mensaje.= 'fecha: '.$fecha .' != '. $rece1_get['fecha'].'<br>';

        if ($venta['numero'] != $rece1_get['numero'])
            $mensaje.= 'numero: '.$venta['numero'] .' != '. $rece1_get['numero'].'<br>';
        if ($venta['cae'] != $rece1_get['cae'])
            $mensaje.= 'cae: '.$venta['cae'] .' != '. $rece1_get['cae'].'<br>';
        if ($venta['total'] != $rece1_get['total'])
            $mensaje.= 'total: '.$venta['total'] .' != '. $rece1_get['total'].'<br>';
        if ($venta['vencimientocae'] != $rece1_get['vencimientocae'])
            $mensaje.= 'vencimientocae: '.$venta['vencimientocae'] .' != '. $rece1_get['vencimientocae'].'<br>';

        return false;
    }

    return true;
}

function actualizar_numero_venta($venta, $ultimonumero, $antiafip)
{
    $comprobaciones = array_sql(consulta_sql(
        "SELECT
            (SELECT idventa FROM ventas
                WHERE estado = 'cerrado'
                    AND estadocae = 'aprobado'
                    AND idtipoventa = {$venta['idtipoventa']}
                    AND numero = ({$venta['numero']} - 1)
                LIMIT 1) AS numero_anterior,
            (SELECT ultimo_idventa_aprobado FROM categorias_ventas
                WHERE idtipoventa = {$venta['idtipoventa']}
                LIMIT 1) AS ultimo_idventa_aprobado,
            (SELECT idventa FROM ventas
                WHERE estado = 'cerrado'
                    AND estadocae = 'aprobado'
                    AND idtipoventa = {$venta['idtipoventa']}
                    AND numero = {$venta['numero']}
                LIMIT 1) AS mismo_numero,
            (SELECT idventa FROM ventas
                WHERE estado = 'cerrado'
                    AND estadocae = 'aprobado'
                    AND idtipoventa = {$venta['idtipoventa']}
                    AND numero = ({$venta['numero']} + 1)
                LIMIT 1) AS numero_siguiente
        "));

    // El último número no está actualizado. DESACTIVADO PORQUE ANULA LAS COMPROBACIONES DE ABAJO. BORRAR SI VA TODO BIEN
    // if ($comprobaciones['ultimo_idventa_aprobado'] && $comprobaciones['ultimo_idventa_aprobado'] != $comprobaciones['numero_anterior']) {
    //     mostrar_error('Ultimo_idventa_aprobado no es el último aprobado<br>'
    //         .'ultimonumero: '.$ultimonumero.'<br>'
    //         .'venta: '.json_encode($venta).'<br>'
    //         .'comprobaciones: '.json_encode($comprobaciones).'<br>'
    //         .'rece1_verificar: '.rece1_verificar($venta['idventa'], false),
    //         true);
    //     return false;
    // }

    // Es el primer comprobante a aprobar
    if (is_numeric($ultimonumero) // Es un número
        && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
        && !$comprobaciones['numero_siguiente'] // NO hay una venta con el número siguiente
        && !$comprobaciones['numero_anterior'] // NO hay una venta con el número anterior
    ) {

        $venta['numero'] = $ultimonumero + 1;
        guardar_sql('ventas', ['numero' => $venta['numero']], $venta['idventa']);
        guardar_sql('ventasxclientes',
            ['numero' => numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])],
            ['idtipoventa' => $venta['idtipoventa'], 'id' => $venta['idventa']]);
        guardar_sql('categorias_ventas', ['ultimonumero' => $venta['numero']], $venta['idtipoventa']);
        return $venta['numero'];
    }

    // Es el número correcto
    if ($venta['numero'] == $ultimonumero + 1 // Es el próximo número a solicitar
        && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
        && !$comprobaciones['numero_siguiente'] // NO hay una venta con el número siguiente
        && $comprobaciones['numero_anterior'] // SI hay una venta con el número anterior
    ) {

        return $venta['numero'];
    }

    // Es el último número aprobado (porque se perdió la conexión de vuelta, pero ARCA llegó a aprobar)
    if ($venta['numero'] == $ultimonumero // Es el mismo número
        && !$comprobaciones['numero_siguiente'] // NO hay una venta con el número siguiente
        && $comprobaciones['numero_anterior'] // SI hay una venta con el número anterior
        && rece1_verificar($comprobaciones['numero_anterior']) // Está ok el número anterior
        && ($antiafip && rece1_verificar($venta['idventa'], false)) // Está ok esta venta excepto el cae y se está ejecutando en antiafip
    ) {

        // En este caso guardo el cae y su vencimiento y no hace falta hacer más nada
        $rece1_get = rece1_get($venta['idcomportamiento'], $venta['puntodeventa'], $venta['numero']);
        if ($rece1_get === false)
            return false;

        guardar_sql(
            'ventas',
            ['estadocae' => 'aprobado', 'cae' => $rece1_get['cae'], 'vencimientocae' => $rece1_get['vencimientocae']],
            $venta['idventa']);
        guardar_sql('categorias_ventas', ['ultimo_idventa_aprobado' => $venta['idventa']], $venta['idtipoventa']);
        return true;
    }

    // Es un número viejo (no se porqué se perdió, pero lo puedo recuperar si todos los datos son iguales)
    if ($venta['numero'] < $ultimonumero + 1 // Es un número anterior
        && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
        && $comprobaciones['numero_siguiente'] // SI hay UNA venta con el número siguiente
        && $comprobaciones['numero_anterior'] // SI hay una venta con el número anterior
        && rece1_verificar($comprobaciones['numero_anterior']) // Está ok el número anterior
        && rece1_verificar($comprobaciones['numero_siguiente']) // Está ok el número siguiente
        && rece1_verificar($venta['idventa'], false) // Está ok esta venta excepto el cae
    ) {

        // En este caso guardo el cae y su vencimiento y no hace falta hacer más nada
        $rece1_get = rece1_get($venta['idcomportamiento'], $venta['puntodeventa'], $venta['numero']);
        if ($rece1_get === false)
            return false;

        guardar_sql(
            'ventas',
            ['estadocae' => 'aprobado', 'cae' => $rece1_get['cae'], 'vencimientocae' => $rece1_get['vencimientocae']],
            $venta['idventa']);
        return true;
    }

    // Es un número viejo (no lo puedo recuperar porque lo hizo con otro sistema o no lo tenemos)
    if ($venta['numero'] < $ultimonumero + 1 // Es un número anterior
        && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
        // && $comprobaciones['numero_siguiente'] // SI hay UNA venta con el número siguiente
        && $comprobaciones['numero_anterior'] // SI hay una venta con el número anterior
        && !rece1_verificar($venta['idventa'], false) // Está ok esta venta excepto el cae
    ) {

        if (sin_control_numeracion($_SESSION['empresa_idempresa'])) {
            $venta['numero'] = $ultimonumero + 1;
            guardar_sql('ventas', [
                'numero' => $venta['numero'],
                'fecha' => date('Y-m-d H:i:s'),
                ], $venta['idventa']);
            guardar_sql('ventasxclientes',
                ['numero' => numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])],
                ['idtipoventa' => $venta['idtipoventa'], 'id' => $venta['idventa']]);
            guardar_sql('categorias_ventas', ['ultimonumero' => $venta['numero']], $venta['idtipoventa']);
            return $venta['numero'];
        }

        mostrar_error('Faltan números correlativos, revisar si tiene que ir a sin_control_numeracion (aparentemente usó otra instancia o sistema)<br>'
            .'ultimonumero: '.$ultimonumero.'<br>'
            .'venta: '.json_encode($venta).'<br>'
            .'comprobaciones: '.json_encode($comprobaciones).'<br>'
            .'rece1_verificar: '.rece1_verificar($venta['idventa'], false),
            true);
        return false;
    }

    // Es un número adelantado (se hicieron facturas en el medio con otro sistema)
    if ($venta['numero'] > $ultimonumero + 1 // Es un número mayor
        && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
        && !$comprobaciones['numero_siguiente'] // NO hay UNA venta con el número siguiente
        && !$comprobaciones['numero_anterior'] // NO hay una venta con el número anterior
        && !rece1_verificar($venta['idventa'], false) // Está ok esta venta excepto el cae
    ) {

/*
        mostrar_error('Numeración faltante en actualizar_numero_venta<br>'
            .'ultimonumero: '.$ultimonumero.'<br>'
            .'venta: '.json_encode($venta).'<br>'
            .'comprobaciones: '.json_encode($comprobaciones).'<br>'
            .'rece1_verificar: '.rece1_verificar($venta['idventa'], false),
            true);
*/
        return false;
    }

    // Es un número que quedó atrás y hay que actualizarlo
    if ($venta['numero'] < $ultimonumero + 1 // Es un número anterior
        && $comprobaciones['mismo_numero'] // SI hay una venta con este número
    ) {

        return $ultimonumero + 1;
    }

    // Es un número adelantado (se nos corrió la numeración por algún problema nuestro)
    $comprobaciones['ultimo_numero'] = campo_sql(consulta_sql(
        "SELECT idventa FROM ventas
            WHERE estado = 'cerrado'
                AND estadocae = 'aprobado'
                AND idtipoventa = {$venta['idtipoventa']}
            ORDER BY numero DESC LIMIT 1"));
    if ($venta['numero'] > $ultimonumero + 1 // Es un número mayor
        && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
        && !$comprobaciones['numero_siguiente'] // NO hay UNA venta con el número siguiente
        && contar_sql(consulta_sql(
            "SELECT idventa FROM ventas
            WHERE idventa = '{$comprobaciones['ultimo_numero']}'
                AND numero = '{$ultimonumero}'")) // SI hay una venta con un número anterior
        && rece1_verificar($comprobaciones['ultimo_numero']) // Está ok la venta anterior
    ) {

        return $ultimonumero + 1;
    }

    // NO es ninguno de los casos anteriores
/*
    mostrar_error('Ninguna de las anteriores en actualizar_numero_venta<br>'
        .'ultimonumero: '.$ultimonumero.'<br>'
        .'venta: '.json_encode($venta).'<br>'
        .'comprobaciones: '.json_encode($comprobaciones),
        true);
*/
    return false;

}

function pyi25($venta, $tipoventa)
{
    $entrada = '';
    //Clave Unica de Identificación Tributaria (C.U.I.T.) del emisor de la factura (11 caracteres)
    $entrada.= $_SESSION['configuracion_cuit'];
    //Código de tipo de comprobante (3 caracteres con nueva RG)
    $entrada.= completar_numero($tipoventa['idcomportamiento'], 3);
    //Punto de venta (5 caracteres con nueva RG)
    $entrada.= completar_numero($tipoventa['puntodeventa'], 5);
    //Código de Autorización de Electrónica (C.A.E.) o Código de Autorización de Impresión (C.A.I.) (14 caracteres)
    $entrada.= $venta['cae'];
    //Fecha de vencimiento (8 caracteres)
    $temp_array = explode(' ', $venta['vencimientocae']);
    $temp_array = explode('-', $temp_array[0]);
    $entrada.= completar_numero($temp_array[0].$temp_array[1].$temp_array[2], 8);

    $png = PATH_WSFE.$_SESSION['configuracion_cuit'].'.png';
    $output = array();
    exec('python '.$_SESSION['path_pyafipws'].'pyi25.py --barras '.$entrada.' --archivo '.$png, $output);

    if (!file_exists($png)) {
        mostrar_error('Error: no encuentro el png:'.$png.'<br>Empresa: '.$_SESSION['configuracion_cuit'].'<br>Factura: '.$tipoventa['nombre'].' '.$tipoventa['letra'].completar_numero($tipoventa['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8).'<br>Código de barras: '.$entrada.'<br>Output: '.json_encode($output), true);
        return false;
    } elseif (!$file = fopen($png, "r")) {
        mostrar_error('Error: no puedo abrir el png:'.$png.'<br>Empresa: '.$_SESSION['configuracion_cuit'].'<br>Factura: '.$tipoventa['nombre'].' '.$tipoventa['letra'].completar_numero($tipoventa['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8).'<br>Código de barras: '.$entrada.'<br>Output: '.json_encode($output), true);
        return false;
    } else {
        $return = '<img src="data:image/png;base64,'.base64_encode(fread($file, filesize($png))).'" alt="'.$entrada.'"><br>'
            .$entrada
            .calcular_digito_verificador_cae($entrada);
        fclose($file);
        unlink($png);
        return $return;
    }
}

function qr_afip($venta, $tipoventa, $solo_enlace = false)
{
    if (mb_strlen($venta['cuit']) == 11) {
        $venta['tipo_doc'] = '80';
        $venta['cuit'] = $venta['cuit'];

    } elseif (mb_strlen($venta['dni']) >= 5) {
        $venta['tipo_doc'] = '96';
        $venta['cuit'] = ceros(11 - mb_strlen($venta['dni'])).$venta['dni'];

    } else {
        $venta['tipo_doc'] = '99';
        $venta['cuit'] = ceros(11);
    }

    $datos_cmp_base_64 = json_encode([
        "ver" => 1,
        "fecha" => substr($venta['fecha'], 0, 10),
        "cuit" => (int) $_SESSION['configuracion_cuit'],
        "ptoVta" => (int) $tipoventa['puntodeventa'],
        "tipoCmp" => (int) $tipoventa['idcomportamiento'],
        "nroCmp" => (int) $venta['numero'],
        "importe" => (float) $venta['total'],
        "moneda" => $venta['codigo'],
        "ctz" => (float) 1,
        "tipoDocRec" => (int) $venta['tipo_doc'],
        "nroDocRec" => (int) $venta['cuit'],
        "tipoCodAut" => "E",
        "codAut" => (int) $venta['cae']
    ]);
    $datos_cmp_base_64 = base64_encode($datos_cmp_base_64);

    $url = 'https://www.afip.gob.ar/fe/qr/';
    $to_qr = $url.'?p='.$datos_cmp_base_64;

    if ($solo_enlace) {
        return $to_qr;
    }

    $barcode = new \Com\Tecnick\Barcode\Barcode();
        $bobj = $barcode->getBarcodeObj(
            'QRCODE,H',                     // barcode type and additional comma-separated parameters
            $to_qr,          // data string to encode
            -4,                             // bar width (use absolute or negative value as multiplication factor)
            -4,                             // bar height (use absolute or negative value as multiplication factor)
            'black',                        // foreground color
            array(-2, -2, -2, -2)           // padding (use absolute or negative values as multiplication factors)
            )->setBackgroundColor('white'); // background color

        // output the barcode as HTML div (see other output formats in the documentation and examples)
        $qr_div = base64_encode($bobj->getPngData());

    return $qr_div;

}

function generar_req($cuit, $razonsocial)
{
    $hostname='www.saasargentina.com';

    $entrada = '[WSAA]
CERT='.PATH_WSFE.$cuit.'.crt
CACERT='.$_SESSION['path_pyafipws'].'conf/afip_ca_info.crt
PRIVATEKEY='.PATH_WSFE.$cuit.'.key
URL=https://wsaa.afip.gov.ar/ws/services/LoginCms

[WSFEv1]
CUIT='.$cuit.'
ENTRADA='.PATH_WSFE.$cuit.'.entrada
SALIDA='.PATH_WSFE.$cuit.'.salida
URL=https://servicios1.afip.gov.ar/wsfev1/service.asmx?WSDL
XML='.PATH_WSFE.'xml/

[WS-SR-PADRON-A4]
CUIT='.$cuit.'
ENTRADA='.PATH_WSFE.$cuit.'.entrada
SALIDA='.PATH_WSFE.$cuit.'.salida
URL=https://aws.afip.gov.ar/sr-padron/webservices/personaServiceA4?wsdl
';

    //Creo el archivo .ini para usar el rece1.py
    $file = fopen(PATH_WSFE.$cuit.'.ini', 'w');
    fwrite($file, $entrada);
    fclose($file);

    //Creo la clave privada
    $output=array();
    exec("openssl genrsa -out ".PATH_WSFE.$cuit.".key 2048", $output);

    //Creo el request para el certificado
    $output=array();
    exec('openssl req -new -key '.PATH_WSFE.$cuit.'.key -subj "/C=AR/O='.$razonsocial.'/CN='.$hostname.'/serialNumber=CUIT '.$cuit.'" -out '.PATH_WSFE.$cuit.'.req', $output);

    subir_wsfe($cuit);
}

function descargar_req($cuit, $crt = false)
{
    existe_wsfe($cuit, 'req');

    if ($crt)
        $ext = '.crt';
    else
        $ext = '.req';

    $download_file = PATH_WSFE.$cuit.$ext;
    $handle = fopen($download_file, "r");
    header("Cache-Control: no-cache, no-store, must-revalidate");
    header("Pragma: no-cache");
    header("Expires: 0");

    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename='.$cuit.$ext);
    header('Content-Transfer-Encoding: binary');
    header('Content-Length: ' . filesize($download_file));
    ob_clean();
    flush();
    readfile($download_file);
    fclose($handle);
}

function calcular_digito_verificador_cae($codigo_barras)
{
    // Etapa 1: comenzar desde la izquierda, sumar todos los caracteres ubicados en las posiciones impares.
    $etapa1 = 0;
    for ($i = 0; $i < strlen($codigo_barras); $i = $i + 2) {
        $etapa1+= (int)$codigo_barras[$i];
    }

    // Etapa 2: Multiplicar la suma obtenida en la etapa 1 por el número 3.
    $etapa2 = $etapa1 * 3;

    // Etapa 3: Comenzar desde la izquierda, sumar todos los caracteres que están ubicados en las posiciones pares.
    $etapa3 = 0;
    for ($i = 1; $i < strlen($codigo_barras); $i = $i + 2) {
        $etapa3+= (int)$codigo_barras[$i];
    }

    // Etapa 4: Sumar los resultados obtenidos en las etapas 2 y 3.
    $etapa4 = $etapa2 + $etapa3;

    // Etapa 5: Buscar el menor número que sumado al resultado obtenido en la etapa 4 dé un número múltiplo de 10. Este será el valor del dígito verificador del módulo 10.
    while ($etapa4 > 10)
        $etapa4-= 10;
    $etapa5 = 10 - $etapa4;

    return $etapa5;
}
